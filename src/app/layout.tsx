import { Toaster } from "@/components/ui/sonner";
import { Analytics } from "@vercel/analytics/react";
import type { Metadata } from "next";
import { Inter, Montserrat } from "next/font/google";
import "./globals.css";

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  display: "swap",
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "ACU AI Career Coach | Discover Your Dream Career",
  description:
    "AI-powered career exploration platform for ACU students. Interactive multi-step form with AI coaching to help high school students discover their dream careers and create personalized career roadmaps.",
  keywords: [
    "ACU",
    "Australian Catholic University",
    "career coach",
    "AI career guidance",
    "student recruitment",
    "career exploration",
    "university pathways",
    "high school students",
  ],
  authors: [{ name: "What's On!" }],
  creator: "What's On!",
  publisher: "Australian Catholic University",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://acu-ai-coach.vercel.app"),
  openGraph: {
    title: "ACU AI Career Coach | Discover Your Dream Career",
    description:
      "AI-powered career exploration platform for ACU students. Interactive multi-step form with AI coaching to help high school students discover their dream careers.",
    url: "https://acu-ai-coach.vercel.app",
    siteName: "ACU AI Career Coach",
    locale: "en_AU",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "ACU AI Career Coach | Discover Your Dream Career",
    description:
      "AI-powered career exploration platform for ACU students. Interactive multi-step form with AI coaching to help high school students discover their dream careers.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en-AU" suppressHydrationWarning>
      <head>
        {/* Preload critical images */}
        <link rel="preload" href="/landing.webp" as="image" />
        <link rel="preload" href="/step1-background.webp" as="image" />
        <link rel="preload" href="/acu-logo.png" as="image" />
        <link rel="preload" href="/whatson-logo.png" as="image" />
      </head>
      <body className={`${montserrat.variable} ${inter.variable} antialiased`}>
        {children}
        <Toaster position="top-right" />
        <Analytics />
      </body>
    </html>
  );
}
