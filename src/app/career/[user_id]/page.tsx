"use client";

import Image from "next/image";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

interface CareerPathData {
  personal_profile: {
    personal_background: string;
    career_vision: string;
  };
  acu_study_path: {
    program_why_acu: string;
    four_year_journey: string;
    entry_pathway: string;
  };
  skills_career_development: {
    skills_development: string;
    career_progression: string;
    industry_readiness: string;
  };
  target_role_deep_dive: {
    role_company_analysis: string;
    success_path_advantages: string;
  };
  acu_experience_learning: {
    campus_life_opportunities: string;
    practical_experiences: string;
    support_systems: string;
  };
  action_plan: {
    market_outlook: string;
    steps_strategy: string;
    success_roadmap: string;
  };
}

interface UserData {
  user_id: string;
  first_name: string;
  last_name: string;
  dream_role: string;
  dream_company: string;
  career_path?: CareerPathData;
}

// Floating animation keyframes
const FloatingCard = ({
  children,
  className = "",
  delay = "0s",
}: {
  children: React.ReactNode;
  className?: string;
  delay?: string;
}) => (
  <div
    className={`transform transition-all duration-700 ease-out animate-fade-in-up ${className}`}
    style={{
      animationDelay: delay,
      animation: `fadeInUp 0.8s ease-out ${delay} both, float 6s ease-in-out infinite`,
    }}
  >
    {children}
    <style jsx>{`
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }
    `}</style>
  </div>
);

// Loading spinner component
const LoadingSpinner = () => (
  <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center relative overflow-hidden">
    {/* Animated background */}
    <div className="absolute inset-0">
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyan-400/20 rounded-full blur-3xl animate-pulse"></div>
      <div
        className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-emerald-400/20 rounded-full blur-3xl animate-pulse"
        style={{ animationDelay: "1s" }}
      ></div>
    </div>

    <div className="text-center z-10">
      <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-cyan-400/20 to-emerald-400/20 backdrop-blur-md border border-white/30 mb-6">
        <div className="w-8 h-8 border-4 border-white/30 border-t-cyan-400 rounded-full animate-spin"></div>
      </div>
      <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
        Crafting Your Epic Future! ✨
      </h2>
      <p className="text-cyan-200 animate-pulse">
        AI is mapping your personalized career roadmap...
      </p>
    </div>
  </div>
);

// Error state component
const ErrorState = ({ error }: { error: string }) => (
  <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
    <div className="text-center max-w-md mx-auto px-6">
      <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-500/20 backdrop-blur-md border border-red-400/30 mb-6">
        <svg
          className="w-8 h-8 text-red-300"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      </div>
      <h2 className="text-2xl font-bold text-white mb-4">
        Oops! Something went wrong 😅
      </h2>
      <p className="text-white/80 mb-6">{error}</p>
      <button
        onClick={() => window.location.reload()}
        className="px-6 py-3 bg-gradient-to-r from-cyan-500/20 to-emerald-500/20 hover:from-cyan-500/30 hover:to-emerald-500/30 text-white rounded-xl border border-white/30 transition-all duration-300 hover:scale-105"
      >
        Try Again 🔄
      </button>
    </div>
  </div>
);

// Interactive section card
const SectionCard = ({
  icon,
  title,
  children,
  bgGradient = "from-purple-500/20 to-blue-500/20",
  delay = "0s",
}: {
  icon: React.ReactNode;
  title: string;
  children: React.ReactNode;
  bgGradient?: string;
  delay?: string;
}) => (
  <FloatingCard delay={delay}>
    <div
      className={`group relative bg-gradient-to-br ${bgGradient} rounded-xl md:rounded-2xl p-5 md:p-7 backdrop-blur-md border border-white/10 hover:border-white/20 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/10`}
    >
      {/* Glow effect on hover */}
      <div className="absolute inset-0 rounded-xl md:rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <div className="relative z-10">
        <div className="flex items-center gap-3 mb-5">
          <div className="w-10 h-10 md:w-12 md:h-12 rounded-lg bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            {icon}
          </div>
          <h2 className="text-lg md:text-xl lg:text-2xl font-bold text-white">
            {title}
          </h2>
        </div>
        {children}
      </div>
    </div>
  </FloatingCard>
);

// Subsection component
const SubSection = ({
  title,
  content,
  delay = "0s",
}: {
  title: string;
  content: string;
  delay?: string;
}) => {
  // Split content by special separator first, then fallback to double line breaks
  let paragraphs;
  if (content.includes("|||PARAGRAPH_BREAK|||")) {
    paragraphs = content
      .split("|||PARAGRAPH_BREAK|||")
      .filter((p) => p.trim().length > 0);
  } else {
    paragraphs = content.split("\n\n").filter((p) => p.trim().length > 0);
  }

  return (
    <div
      className="group p-4 md:p-5 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 transition-all duration-300 hover:scale-[1.01]"
      style={{ animationDelay: delay }}
    >
      <h3 className="text-base md:text-lg font-semibold text-white mb-2 group-hover:text-purple-200 transition-colors duration-300">
        {title}
      </h3>
      <div className="text-sm md:text-base text-white/80 leading-relaxed group-hover:text-white/90 transition-colors duration-300 space-y-3">
        {paragraphs.map((paragraph, index) => (
          <p key={index} className="leading-relaxed">
            {paragraph.trim()}
          </p>
        ))}
      </div>
    </div>
  );
};

export default function CareerPathPage() {
  const params = useParams();
  const user_id = params.user_id as string;
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user_id) return;

      try {
        const response = await fetch(`/api/users/${user_id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError(
              "Career path not found. Please check your link or contact support.",
            );
          } else {
            setError(
              "Unable to load your career path. Please try again later.",
            );
          }
          return;
        }

        const data = await response.json();

        if (!data.career_path) {
          setError(
            "Your career path is still being generated. Please try again in a few moments.",
          );
          return;
        }

        setUserData(data);
      } catch (err) {
        setError(
          "Unable to load your career path. Please check your connection and try again.",
        );
        console.error("Error fetching user data:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [user_id]);

  if (isLoading) return <LoadingSpinner />;
  if (error || !userData)
    return <ErrorState error={error || "Career path not found"} />;

  const careerPath = userData.career_path as CareerPathData;

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden">
      {/* Fixed Header Banner */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            {/* ACU Logo - Left */}
            <div className="flex items-center">
              <Image
                src="/acu-logo.png"
                alt="Australian Catholic University"
                width={120}
                height={40}
                className="h-8 md:h-10 w-auto"
              />
            </div>

            {/* WhatsOn Logo - Right */}
            <div className="flex items-center">
              <Image
                src="/whatson-logo.png"
                alt="WhatsOn"
                width={80}
                height={26}
                className="h-5 md:h-6 w-auto -translate-y-0.5"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-cyan-400/15 rounded-full blur-3xl animate-pulse"></div>
        <div
          className="absolute bottom-0 right-1/4 w-96 h-96 bg-pink-400/15 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-1/2 left-0 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "4s" }}
        ></div>
        <div
          className="absolute top-1/3 right-0 w-96 h-96 bg-green-400/10 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "6s" }}
        ></div>
      </div>

      {/* Hero Section */}
      <div className="relative z-10 pt-16 md:pt-20">
        <FloatingCard delay="0.2s">
          <div className="bg-gradient-to-r from-white/15 to-white/5 backdrop-blur-xl border-b border-white/20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-20 h-20 md:w-24 md:h-24 rounded-full bg-gradient-to-r from-cyan-400/20 to-blue-400/20 backdrop-blur-md border border-white/30 mb-6 hover:scale-110 transition-transform duration-300">
                  <Image
                    src="/progress-indicator/ai-coach.png"
                    alt="ACU Logo"
                    width={60}
                    height={60}
                    className="rounded-full"
                  />
                </div>
                <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-4 bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 bg-clip-text text-transparent">
                  {userData.first_name} {userData.last_name}
                </h1>
                <p className="text-lg md:text-2xl text-white/90 mb-2">
                  Your Epic Four Year Journey Starts Here! 🚀
                </p>
                <p className="text-lg md:text-xl text-cyan-200 mb-6 font-medium">
                  Future {userData.dream_role} at {userData.dream_company}
                </p>
                <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-emerald-500/30 to-cyan-500/30 rounded-full border border-emerald-400/30 backdrop-blur-md">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                  <span className="text-white text-sm md:text-base font-medium">
                    AI-Powered • Tailored Just for You ✨
                  </span>
                </div>
              </div>
            </div>
          </div>
        </FloatingCard>

        {/* Content */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-10 space-y-6 md:space-y-10">
          {/* Personal Profile */}
          <SectionCard
            icon={
              <svg
                className="w-6 h-6 md:w-7 md:h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            }
            title="🌟 Your Amazing Profile"
            bgGradient="from-pink-500/30 to-rose-500/30"
            delay="0.3s"
          >
            <div className="space-y-4 md:space-y-5">
              <SubSection
                title="Your Background & Superpowers"
                content={careerPath.personal_profile.personal_background}
                delay="0.4s"
              />
              <SubSection
                title="Your Tech Dreams & Vision"
                content={careerPath.personal_profile.career_vision}
                delay="0.5s"
              />
            </div>
          </SectionCard>

          {/* Study Path */}
          <SectionCard
            icon={
              <svg
                className="w-6 h-6 md:w-7 md:h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
            }
            title="🎓 Your Epic Study Path"
            bgGradient="from-cyan-500/30 to-blue-500/30"
            delay="0.4s"
          >
            <div className="space-y-4 md:space-y-5">
              <SubSection
                title="Why ACU is Perfect for You"
                content={careerPath.acu_study_path.program_why_acu}
                delay="0.5s"
              />
              <SubSection
                title="Your 4-Year Journey Map"
                content={careerPath.acu_study_path.four_year_journey}
                delay="0.6s"
              />
              <SubSection
                title="Getting In - Your Pathway"
                content={careerPath.acu_study_path.entry_pathway}
                delay="0.7s"
              />
            </div>
          </SectionCard>

          {/* Skills & Career Development */}
          <SectionCard
            icon={
              <svg
                className="w-6 h-6 md:w-7 md:h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
            }
            title="💪 Skills & Career Growth"
            bgGradient="from-emerald-500/30 to-teal-500/30"
            delay="0.5s"
          >
            <div className="space-y-4 md:space-y-5">
              <SubSection
                title="Skills You'll Master"
                content={
                  careerPath.skills_career_development.skills_development
                }
                delay="0.7s"
              />
              <SubSection
                title="Your Career Ladder"
                content={
                  careerPath.skills_career_development.career_progression
                }
                delay="0.8s"
              />
              <SubSection
                title="Ready for the Real World"
                content={
                  careerPath.skills_career_development.industry_readiness
                }
                delay="0.9s"
              />
            </div>
          </SectionCard>

          {/* Dream Role Deep Dive */}
          <SectionCard
            icon={
              <svg
                className="w-6 h-6 md:w-7 md:h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                />
              </svg>
            }
            title="⭐ Your Dream Role"
            bgGradient="from-purple-500/30 to-pink-500/30"
            delay="0.8s"
          >
            <div className="space-y-4 md:space-y-5">
              <SubSection
                title="Your Target Role & Company"
                content={careerPath.target_role_deep_dive.role_company_analysis}
                delay="0.9s"
              />
              <SubSection
                title="Your Success Path & Edge"
                content={
                  careerPath.target_role_deep_dive.success_path_advantages
                }
                delay="1.0s"
              />
            </div>
          </SectionCard>

          {/* ACU Experience */}
          <SectionCard
            icon={
              <svg
                className="w-6 h-6 md:w-7 md:h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
            }
            title="🏫 Your ACU Experience"
            bgGradient="from-orange-500/30 to-yellow-500/30"
            delay="1.0s"
          >
            <div className="space-y-4 md:space-y-5">
              <SubSection
                title="Campus Life & Cool Opportunities"
                content={
                  careerPath.acu_experience_learning.campus_life_opportunities
                }
                delay="1.0s"
              />
              <SubSection
                title="Hands-On Learning & Real Projects"
                content={
                  careerPath.acu_experience_learning.practical_experiences
                }
                delay="1.1s"
              />
              <SubSection
                title="Support & Community"
                content={careerPath.acu_experience_learning.support_systems}
                delay="1.2s"
              />
            </div>
          </SectionCard>

          {/* Action Plan */}
          <SectionCard
            icon={
              <svg
                className="w-6 h-6 md:w-7 md:h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            }
            title="🚀 Your Action Plan"
            bgGradient="from-indigo-500/30 to-purple-500/30"
            delay="1.2s"
          >
            <div className="space-y-4 md:space-y-5">
              <SubSection
                title="Tech Industry Outlook"
                content={careerPath.action_plan.market_outlook}
                delay="1.1s"
              />
              <SubSection
                title="Your Next Steps & Strategy"
                content={careerPath.action_plan.steps_strategy}
                delay="1.2s"
              />
              <SubSection
                title="Your Success Roadmap"
                content={careerPath.action_plan.success_roadmap}
                delay="1.3s"
              />

              {/* Hackathon Poster Link */}
              <div className="mt-6">
                <a
                  href="https://www.acu.edu.au/study-at-acu/find-a-course/new-courses/computer-science/computer-hackathon"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block hover:opacity-90 transition-opacity group"
                >
                  <Image
                    src="/hackathon-poster.webp"
                    alt="Air Aware Hackathon 2025 Poster"
                    width={800}
                    height={450}
                    className="rounded-lg shadow-lg w-full max-w-xl mx-auto h-auto object-cover group-hover:shadow-xl transition-shadow duration-300"
                    priority
                  />
                  <p className="text-center mt-3 text-sm md:text-base text-white/90 group-hover:text-white transition-colors duration-300 font-semibold group-hover:underline">
                    Join the Air Aware Hackathon 2025 (July 7-8)! Use your AI
                    skills to tackle air pollution and shape a cleaner future.
                    Click to learn more.
                  </p>
                </a>
              </div>
            </div>
          </SectionCard>

          {/* Call to Action - Updated without PDF button */}
          <FloatingCard delay="1.1s">
            <div className="text-center py-8 md:py-12">
              <div className="bg-gradient-to-r from-white/15 to-white/10 backdrop-blur-xl rounded-2xl md:rounded-3xl p-8 md:p-12 border border-white/30 hover:border-white/40 transition-all duration-500 hover:scale-[1.02] group">
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-full bg-gradient-to-r from-emerald-400/30 to-cyan-400/30 backdrop-blur-md border border-white/30 mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg
                      className="w-8 h-8 md:w-10 md:h-10 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                    Ready to Launch Your Future? 🚀
                  </h3>
                  <p className="text-white/90 text-lg md:text-xl mb-8 max-w-2xl mx-auto">
                    This is YOUR personalized roadmap to an epic tech career!
                    ACU&apos;s AI Coach has mapped out your path to success.
                    Let&apos;s make it happen! 💫
                  </p>
                </div>

                <div className="flex justify-center">
                  <a
                    href="https://acu.edu.au/computer-science"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group/btn inline-flex items-center gap-3 bg-gradient-to-r from-emerald-500 to-cyan-500 hover:from-emerald-400 hover:to-cyan-400 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-emerald-500/25 border border-white/20"
                  >
                    <span>Apply to ACU Now!</span>
                  </a>
                </div>
              </div>
            </div>
          </FloatingCard>
        </div>
      </div>
    </div>
  );
}
