"use client";

import Footer from "@/components/Footer";
import Header from "@/components/Header";
import Step0 from "@/components/steps/Step0";
import Step1 from "@/components/steps/Step1";
import Step2 from "@/components/steps/Step2";
import Step3 from "@/components/steps/Step3";
import Step4 from "@/components/steps/Step4";
import Step5 from "@/components/steps/Step5";
import Step6 from "@/components/steps/Step6";
import { useFormStore } from "@/lib/store";

export default function Home() {
  const { currentStep, setCurrentStep, resetForm } = useFormStore();

  const goToNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const goToPrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleFinish = () => {
    // TODO: Handle form submission
    console.log("Form submitted!");
    // Reset to first step or show success message
    setCurrentStep(0);
  };

  const goBackToStart = () => {
    resetForm();
    setCurrentStep(0);
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return <Step0 onNext={goToNext} />;
      case 1:
        return <Step1 onNext={goToNext} onBack={goToPrevious} />;
      case 2:
        return <Step2 onNext={goToNext} onBack={goToPrevious} />;
      case 3:
        return <Step3 onNext={goToNext} onBack={goToPrevious} />;
      case 4:
        return <Step4 />;
      case 5:
        return <Step5 onNext={goToNext} />;
      case 6:
        return <Step6 onFinish={handleFinish} />;
      default:
        return <Step0 onNext={goToNext} />;
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <Header onBackToStart={goBackToStart} />
      <main className="flex-1 relative">{renderCurrentStep()}</main>
      <Footer />
    </div>
  );
}
