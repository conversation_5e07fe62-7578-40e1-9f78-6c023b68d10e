import fs from "fs";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import { Resend } from "resend";

const resend = new Resend(process.env.RESEND_API_KEY);

interface EmailRequest {
  email: string;
  firstName: string;
  lastName: string;
  careerPathUrl: string;
}

const createWelcomeEmailHTML = (firstName: string, careerPathUrl: string) => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Your ACU Journey!</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.5; 
            color: #333; 
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            -webkit-text-size-adjust: 100%;
        }
        .email-wrapper {
            width: 100%;
            max-width: 100%;
            background-color: #f8f9fa;
            padding: 10px;
        }
        .container { 
            background: white; 
            padding: 20px; 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }
        .header { 
            text-align: center; 
            margin-bottom: 25px; 
            background: linear-gradient(135deg, #391460, #6B46C1);
            color: white;
            padding: 25px 20px;
            border-radius: 8px;
            margin: -20px -20px 25px -20px;
        }

        .header h1 { 
            margin: 15px 0 0 0; 
            font-size: 24px; 
            font-weight: bold;
            line-height: 1.3;
        }
        .header p { 
            margin: 8px 0 0 0; 
            opacity: 0.9;
            font-size: 15px;
        }
        .content { 
            margin: 20px 0; 
            font-size: 17px;
            line-height: 1.6;
        }
        .content p {
            margin: 15px 0;
        }
        .cta-button { 
            display: inline-block; 
            background: linear-gradient(135deg, #10B981, #059669);
            color: white !important; 
            padding: 18px 35px; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            transition: transform 0.2s;
            width: 100%;
            max-width: 300px;
            box-sizing: border-box;
        }
        .cta-button:hover {
            transform: translateY(-1px);
        }
        .cta-container { 
            text-align: center; 
            margin: 30px 0;
            padding: 25px 20px;
            background: linear-gradient(135deg, #F0FDF4, #ECFDF5);
            border-radius: 8px;
            border-left: 4px solid #10B981;
        }
        .footer { 
            margin-top: 35px; 
            padding-top: 20px; 
            border-top: 2px solid #E5E7EB;
            font-size: 14px; 
            color: #6B7280;
            text-align: center;
        }
        .powered-by {
            text-align: center;
            margin: 15px 0;
            font-size: 14px;
            color: #6B7280;
            font-weight: 500;
        }
        .highlight { 
            background: linear-gradient(135deg, #FEF3C7, #FDE68A);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #F59E0B;
            margin: 20px 0;
        }
        .ai-disclaimer {
            background: linear-gradient(135deg, #EFF6FF, #DBEAFE);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
            margin: 20px 0;
            font-size: 14px;
            color: #1E40AF;
        }
        .emoji { font-size: 18px; }
        
        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 5px;
            }
            .container {
                padding: 15px;
                border-radius: 8px;
            }
            .header {
                margin: -15px -15px 20px -15px;
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 22px;
            }
            .content {
                font-size: 16px;
            }
            .cta-button {
                padding: 16px 25px;
                font-size: 16px;
                width: 100%;
                max-width: none;
            }

        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="container">
            <div class="header">
                <h1><span class="emoji">🎉</span> Welcome to Your Epic Future, ${firstName}!</h1>
                <p>Your personalized ACU journey starts here</p>
            </div>
            
            <div class="content">
                <p>G'day ${firstName}!</p>
                
                <p>Congratulations! <span class="emoji">✨</span> You've just taken the first step towards an absolutely brilliant career in tech. We're genuinely excited to be part of your journey and help you explore the amazing opportunities at Australian Catholic University!</p>
                
                <div class="highlight">
                    <p><strong><span class="emoji">🚀</span> Your AI-Powered Career Roadmap is Ready!</strong></p>
                    <p>Our advanced AI technology has crafted a completely personalized career path just for you, showing exactly how ACU's Computer Science & Data Science programs will launch you towards your dream role. This roadmap is tailored to your unique interests, goals, and aspirations.</p>
                </div>
                
                <p>Your comprehensive roadmap includes detailed insights into:</p>
                <ul style="margin: 15px 0; padding-left: 20px; line-height: 1.7;">
                    <li><span class="emoji">🎯</span> <strong>Your personal profile & career vision</strong> - Understanding your unique strengths and aspirations</li>
                    <li><span class="emoji">🎓</span> <strong>Complete ACU study pathway</strong> - Your 4-year journey through the double degree program</li>
                    <li><span class="emoji">💪</span> <strong>Skills development & career progression</strong> - Technical and professional capabilities you'll gain</li>
                    <li><span class="emoji">⭐</span> <strong>Deep dive into your dream role</strong> - Detailed analysis of your target career and company</li>
                    <li><span class="emoji">🏫</span> <strong>ACU experience & opportunities</strong> - Campus life, industry connections, and unique advantages</li>
                    <li><span class="emoji">📈</span> <strong>Action plan for success</strong> - Concrete steps and strategies to achieve your goals</li>
                </ul>
                
                <div class="cta-container">
                    <p style="margin-bottom: 20px; font-weight: 600; color: #065F46; font-size: 16px;">
                        <span class="emoji">📋</span> Ready to explore your personalized career roadmap?
                    </p>
                    <a href="${careerPathUrl}" class="cta-button">
                        View Your Career Roadmap <span class="emoji">🚀</span>
                    </a>
                </div>
                
                <p>We've also attached ACU's detailed program brochure so you can learn more about all the incredible opportunities waiting for you, including industry partnerships, cutting-edge facilities, and career support services.</p>
                
                <div class="ai-disclaimer">
                    <p style="margin: 0; font-weight: 600;"><span class="emoji">🤖</span> AI-Generated Content Disclaimer</p>
                    <p style="margin: 8px 0 0 0;">This career roadmap was generated using artificial intelligence technology to provide personalized guidance based on your responses. While we strive for accuracy, the information should be considered as guidance only. We recommend discussing your career plans with ACU's academic advisors for the most current and detailed information about programs and pathways.</p>
                </div>
                
                <p>This is just the beginning of your amazing tech journey. ACU is here to support you every step of the way, from your first day of study to launching your career! <span class="emoji">🌟</span></p>
            </div>
            
            <div class="powered-by">
                Powered by What's On!
            </div>
            
            <div class="footer">
                <p><strong>Australian Catholic University</strong></p>
                <p>Empowering students to make a meaningful difference</p>
                <p style="margin-top: 15px; font-size: 12px; line-height: 1.4;">
                    This email was sent to you because you requested a personalized career roadmap from ACU. The content was generated using AI technology to provide personalized guidance based on your responses.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
  `;
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, firstName, careerPathUrl }: EmailRequest = body;

    if (!email || !firstName || !careerPathUrl) {
      return NextResponse.json(
        { error: "Missing required fields: email, firstName, careerPathUrl" },
        { status: 400 },
      );
    }

    // Check if API key is configured
    if (!process.env.RESEND_API_KEY) {
      console.error("RESEND_API_KEY is not configured");
      return NextResponse.json(
        { error: "Email service not configured" },
        { status: 500 },
      );
    }

    // Check if from email is configured
    if (!process.env.RESEND_FROM_EMAIL) {
      console.error("RESEND_FROM_EMAIL is not configured");
      return NextResponse.json(
        { error: "Email service not configured" },
        { status: 500 },
      );
    }

    // Read ACU brochure PDF
    let pdfAttachment = null;
    try {
      const pdfPath = path.join(process.cwd(), "docs", "acu-brochure.pdf");
      const pdfBuffer = fs.readFileSync(pdfPath);
      pdfAttachment = {
        filename: "ACU-Computer-Science-Data-Science-Brochure.pdf",
        content: pdfBuffer,
      };
      console.log("ACU brochure PDF loaded for email attachment");
    } catch (error) {
      console.warn("Failed to load ACU brochure PDF:", error);
      // Continue without attachment if PDF is not available
    }

    // Generate email HTML
    const emailHtml = createWelcomeEmailHTML(firstName, careerPathUrl);

    // Send email using Resend
    const emailData = {
      from: process.env.RESEND_FROM_EMAIL,
      to: [email],
      subject: `🎉 ${firstName}, Your ACU Career Roadmap is Ready!`,
      html: emailHtml,
      attachments: [] as Array<{ filename: string; content: Buffer }>,
    };

    // Add PDF attachment if available
    if (pdfAttachment) {
      emailData.attachments = [pdfAttachment];
    }

    const { data, error } = await resend.emails.send(emailData);

    if (error) {
      console.error("Resend API error:", error);
      return NextResponse.json(
        { error: "Failed to send email", details: error.message },
        { status: 500 },
      );
    }

    console.log("Welcome email sent successfully:", data?.id);
    return NextResponse.json(
      {
        success: true,
        message: "Welcome email sent successfully",
        emailId: data?.id,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Email sending error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
