import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

const saveQuestionSchema = z.object({
  user_id: z.string().min(1, "user_id is required"),
  first_name: z.string().min(1, "first_name is required"),
  last_name: z.string().min(1, "last_name is required"),
  question: z.string().min(1, "question is required"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body using Zod
    const validationResult = saveQuestionSchema.safeParse(body);

    if (!validationResult.success) {
      const missingFields = validationResult.error.errors
        .map((err) => err.path[0])
        .join(", ");
      return NextResponse.json(
        {
          error: `Missing or invalid fields: ${missingFields}`,
          details: validationResult.error.errors,
        },
        { status: 400 },
      );
    }

    const { user_id, first_name, last_name, question } = validationResult.data;

    // Insert question into database using Prisma
    await prisma.userQuestion.create({
      data: {
        user_id,
        first_name,
        last_name,
        question,
      },
    });

    console.log(
      "Question saved for user:",
      user_id,
      `(${first_name} ${last_name})`,
    );
    return NextResponse.json(
      {
        success: true,
        message: "Question saved successfully",
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
