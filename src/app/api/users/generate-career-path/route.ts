import { prisma } from "@/lib/prisma";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateObject } from "ai";
import fs from "fs";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import QRCode from "qrcode";
import { z } from "zod";

// Define the career path schema
const careerPathSchema = z.object({
  personal_profile: z.object({
    personal_background: z
      .string()
      .describe(
        "Comprehensive summary combining student's background, interests, and how they align with computer science and data science",
      ),
    career_vision: z
      .string()
      .describe("Analysis of their motivation and career aspirations in tech"),
  }),

  acu_study_path: z.object({
    program_why_acu: z
      .string()
      .describe(
        "Combined explanation of ACU's double degree program and why ACU is perfect for this student",
      ),
    four_year_journey: z
      .string()
      .describe(
        "Complete 4-year academic journey from foundation to masters, including key milestones and course progression",
      ),
    entry_pathway: z
      .string()
      .describe("Entry requirements and pathway information"),
  }),

  skills_career_development: z.object({
    skills_development: z
      .string()
      .describe(
        "Combined description of technical and professional skills development throughout the program",
      ),
    career_progression: z
      .string()
      .describe(
        "Career opportunities from graduation through long-term vision, including salary progression",
      ),
    industry_readiness: z
      .string()
      .describe("How the program prepares students for industry demands"),
  }),

  target_role_deep_dive: z.object({
    role_company_analysis: z
      .string()
      .describe(
        "Combined deep dive into their dream role, responsibilities, and how ACU prepares them for their target company",
      ),
    success_path_advantages: z
      .string()
      .describe(
        "Specific pathway from ACU to achieving their dream role and competitive advantages ACU graduates have",
      ),
  }),

  acu_experience_learning: z.object({
    campus_life_opportunities: z
      .string()
      .describe(
        "Combined description of campus life, community, and unique opportunities only available at ACU",
      ),
    practical_experiences: z
      .string()
      .describe(
        "Description of hands-on learning opportunities, industry connections, and research",
      ),
    support_systems: z
      .string()
      .describe(
        "Support systems, values education, and global experiences available",
      ),
  }),

  action_plan: z.object({
    market_outlook: z
      .string()
      .describe(
        "Industry landscape, job market trends, and emerging technologies outlook",
      ),
    steps_strategy: z
      .string()
      .describe(
        "Combined immediate steps and integrated strategy for optimizing studies and career preparation",
      ),
    success_roadmap: z
      .string()
      .describe(
        "Personal roadmap for achieving their career goals with networking strategy",
      ),
  }),
});

// Function to send welcome email asynchronously
async function sendWelcomeEmailAsync(
  userId: string,
  firstName: string,
  lastName: string,
  email: string,
) {
  if (!email || !firstName) {
    console.warn("Missing email or firstName for welcome email");
    return;
  }

  try {
    const baseUrl =
      process.env.NODE_ENV === "production"
        ? "https://acu.knowwhatson.com"
        : "http://localhost:3000";
    const careerPathUrl = `${baseUrl}/career/${userId}`;

    const response = await fetch(`${baseUrl}/api/send-welcome-email`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email,
        firstName,
        lastName,
        careerPathUrl,
      }),
    });

    const result = await response.json();

    if (response.ok) {
      console.log(
        "Background welcome email sent successfully:",
        result.emailId,
      );
    } else {
      console.error("Background welcome email failed:", result.error);
    }
  } catch (error) {
    console.error("Error sending background welcome email:", error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id } = body;

    if (!user_id) {
      return NextResponse.json(
        { error: "user_id is required" },
        { status: 400 },
      );
    }

    // Check if API key is available
    const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!apiKey) {
      console.error("GOOGLE_API_KEY is not set");
      return NextResponse.json(
        { error: "API key not configured" },
        { status: 500 },
      );
    }

    // Get user information using Prisma
    const user = await prisma.user.findUnique({
      where: {
        user_id: user_id,
      },
    });

    if (!user) {
      console.error("User not found:", user_id);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Read ACU information from the documentation file
    let acuInfo = "";
    try {
      const acuFilePath = path.join(process.cwd(), "docs", "acu.txt");
      acuInfo = fs.readFileSync(acuFilePath, "utf-8");
      console.log("ACU documentation loaded for career path generation");
    } catch (error) {
      console.warn("Failed to load ACU documentation:", error);
      acuInfo = "ACU documentation not available";
    }

    // Create Google provider instance
    const google = createGoogleGenerativeAI({
      apiKey: apiKey,
    });

    // Generate career path using AI
    const result = await generateObject({
      model: google("gemini-2.5-flash-preview-05-20"),
      schema: careerPathSchema,
      prompt: `Generate a personalized career roadmap for this ACU Computer Science & Data Science student. Use simple, everyday words (say "complete" not "comprehensive", "help" not "facilitate")

STUDENT INFO:
- Name: ${user.first_name} ${user.last_name}
- High School: ${user.high_school}
- Dream Role: ${user.dream_role}
- Dream Company: ${user.dream_company}
- Interests: ${user.selected_interests?.join(", ") || "Not specified"}

ACU DOCUMENTATION:
${acuInfo}

CRITICAL KEY MESSAGES TO INCORPORATE:
- ACU offers NSW's ONLY fast-tracked double degree in Computer and Data Science
- Students can earn both a Bachelor of Computer Science and Master of Data Science in just 4 years
- This globally focused double degree offers technical expertise and ethical innovation
- ACU integrates ethics and social responsibility into every course
- Mention scholarship opportunities and industry internships/placements subtly throughout
- Emphasize the accelerated academic and professional journey advantage

CRITICAL INSTRUCTIONS FOR USING ACU INFORMATION:
- Extract specific details from the ACU documentation above
- Use concrete facts like course codes, campus features, industry partnerships
- Never say you don't have information - use what's provided in the documentation
- Reference specific programs, facilities, and opportunities mentioned in the ACU materials
- Extract and use real course codes, campus names, and industry connections from the documentation

WRITING STYLE FOR HIGH SCHOOL STUDENTS:
- Use simple, everyday words (say "complete" not "comprehensive", "help" not "facilitate")
- Be friendly, engaging and exciting like talking to a mate
- Use Australian English spelling and expressions (e.g., "colour" not "color", "centre" not "center", "uni", "awesome", "brilliant")
- Include specific details extracted from the ACU documentation
- Keep each section detailed and interesting (4-6 sentences per subsection for more comprehensive coverage)
- Use concrete examples they can understand and relate to
- Structure with short, clear paragraphs
- Make it sound exciting and achievable - this is about their amazing future!
- NO ACADEMIC JARGON - keep it conversational and enthusiastic

FOCUS ON PROMOTING ACU'S ADVANTAGES:
- Always highlight what makes ACU special and better than other unis, especially the NSW-only fast-track advantage
- Emphasize ACU's unique selling points from the documentation
- Show how ACU sets students up for success better than anywhere else
- Make ACU sound like the obvious best choice for their future
- Connect every advantage back to real career benefits
- Subtly weave in scholarship opportunities and internship/placement benefits

SECTIONS TO GENERATE:

1. PERSONAL PROFILE (2 subsections):
   - Personal Background: Connect their interests to tech/data science using exciting language and specific examples (4-5 sentences)
   - Career Vision: Explain their tech career goals with ACU connections and why it's totally achievable (4-5 sentences)

2. ACU STUDY PATH (3 subsections):
   - Program & Why ACU: Explain the double degree using specific ACU advantages, industry partners, and why ACU is brilliant for this (5-6 sentences)
   - 4-Year Journey: Detail study progression with course information, highlighting exciting opportunities each year. CRITICAL: MUST be structured as FIVE completely separate paragraphs. Use the exact separator "|||PARAGRAPH_BREAK|||" between each paragraph. Keep each paragraph SHORT and concise. Format EXACTLY like this:

Overview paragraph content here (1-2 sentences about the entire journey)|||PARAGRAPH_BREAK|||First year paragraph content here (1-2 sentences about first year courses)|||PARAGRAPH_BREAK|||Second year paragraph content here (1-2 sentences about second year courses)|||PARAGRAPH_BREAK|||Third year paragraph content here (1-2 sentences about third year courses)|||PARAGRAPH_BREAK|||Fourth year paragraph content here (1-2 sentences about fourth year courses)

You MUST use the exact separator "|||PARAGRAPH_BREAK|||" between paragraphs. Keep each paragraph SHORT - maximum 1-2 sentences each. Do not use any other formatting.
   - Entry Pathway: Requirements and application info with encouraging tips and pathway options (4-5 sentences)

3. SKILLS & CAREER DEVELOPMENT (3 subsections):
   - Skills Development: Specific skills with course examples and practical uses that sound exciting (5-6 sentences)
   - Career Progression: Job opportunities with realistic salary info, industry connections, and awesome career prospects (5-6 sentences)
   - Industry Readiness: How ACU prepares students better than other unis with specific examples (4-5 sentences)

4. TARGET ROLE DEEP DIVE (2 subsections):
   - Role & Company Analysis: Detailed explanation of their dream job and target company with enthusiasm (5-6 sentences)
   - Success Path & Advantages: How ACU specifically gives them the edge using real partnerships and unique opportunities (5-6 sentences)

5. ACU EXPERIENCE & LEARNING (3 subsections):
   - Campus Life & Opportunities: Campus experience using specific features, events like hackathons, and what makes ACU special (5-6 sentences)
   - Practical Experiences: Hands-on learning with industry partners, real projects, and unique ACU opportunities (5-6 sentences)
   - Support Systems: Student support with specific ACU services and community that sets them up for success (4-5 sentences)

6. ACTION PLAN (3 subsections):
   - Market Outlook: Tech opportunities with trends, Australian job market data, and why it's an exciting time (5-6 sentences)
   - Steps & Strategy: Actionable steps using ACU resources, pathway options, and how to make the most of ACU (5-6 sentences)
   - Success Roadmap: Timeline using ACU milestones, opportunities, and how ACU leads them to their dream career (5-6 sentences)

REQUIREMENTS:
- Extract and use specific course codes, campus names, and industry partnerships from the ACU documentation
- Reference actual facilities and opportunities described in the provided materials
- Use encouraging, simple language throughout
- Make everything feel personal and achievable
- Connect all details to the student's specific interests and goals
- Never say information is not available - use what's in the ACU documentation provided
    `,
    });

    const careerPath = result.object;

    // Update user record with career path using Prisma
    try {
      await prisma.user.update({
        where: {
          user_id: user_id,
        },
        data: {
          career_path: careerPath,
        },
      });
    } catch (updateError) {
      console.error("Failed to update career path:", updateError);
      return NextResponse.json(
        {
          error: "Failed to save career path",
          details:
            updateError instanceof Error
              ? updateError.message
              : "Unknown error",
        },
        { status: 500 },
      );
    }

    console.log("AI-generated career path created for user:", user_id);

    // Send welcome email asynchronously (don't wait for it)
    sendWelcomeEmailAsync(
      user_id,
      user.first_name,
      user.last_name,
      user.email,
    ).catch((error: unknown) => {
      console.error("Background email sending failed:", error);
    });

    // Generate QR code pointing to user's career path page
    const baseUrl =
      process.env.NODE_ENV === "production"
        ? "https://acu.knowwhatson.com"
        : "http://localhost:3000";
    const careerPathUrl = `${baseUrl}/career/${user_id}`;

    const qrCodeSvg = await QRCode.toString(careerPathUrl, {
      type: "svg",
      width: 200,
      margin: 2,
      color: {
        dark: "#391460",
        light: "#FFFFFF",
      },
    });

    // Update user record with QR code using Prisma
    try {
      await prisma.user.update({
        where: {
          user_id: user_id,
        },
        data: {
          qr_code: qrCodeSvg,
        },
      });
    } catch (qrUpdateError) {
      console.error("Failed to update QR code:", qrUpdateError);
      // Don't fail the entire operation for QR code issues
      console.warn(
        "QR code update failed, but career path was saved successfully",
      );
    }

    return NextResponse.json(
      {
        success: true,
        career_path: careerPath,
        qr_code: qrCodeSvg,
        career_path_url: careerPathUrl,
        message: "Career path generated successfully",
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
