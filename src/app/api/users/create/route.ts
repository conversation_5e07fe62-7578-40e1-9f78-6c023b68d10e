import { prisma, type UserCreateInput } from "@/lib/prisma";
import { nanoid } from "nanoid";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Generate 8-character user ID
    const userId = nanoid(8);

    // Prepare user data
    const userData: UserCreateInput = {
      user_id: userId,
      first_name: body.firstName || "",
      last_name: body.lastName || "",
      email: body.email || "",
      high_school: body.highSchool || "",
      dream_role: body.dreamRole || "",
      dream_company: body.dreamCompany || "",
      selected_interests: body.selectedInterests || [],
    };

    // Insert user into database using Prisma
    await prisma.user.create({
      data: userData,
    });

    console.log("User created successfully:", userId);
    return NextResponse.json(
      {
        success: true,
        user_id: userId,
        message: "User created successfully",
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
