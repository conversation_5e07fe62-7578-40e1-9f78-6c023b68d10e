import Image from "next/image";
import React from "react";

interface ProgressIndicatorProps {
  currentStep: number; // 1-5 representing the current active step
}

export default function ProgressIndicator({
  currentStep,
}: ProgressIndicatorProps) {
  const steps = [
    {
      id: 1,
      name: "Profile",
      iconSrc: "/progress-indicator/profile.png",
    },
    {
      id: 2,
      name: "Dream Job",
      iconSrc: "/progress-indicator/dream-job.png",
    },
    {
      id: 3,
      name: "Interests",
      iconSrc: "/progress-indicator/interests.png",
    },
    {
      id: 4,
      name: "AI Coach",
      iconSrc: "/progress-indicator/ai-coach.png",
    },
    {
      id: 5,
      name: "Career Roadmap",
      iconSrc: "/progress-indicator/career-roadmap.png",
    },
  ];

  return (
    <div className="w-[900px]">
      {/* Icons Row with Connector Lines */}
      <div className="flex items-center mb-3">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            {/* Step Icon Container */}
            <div className="flex justify-center w-[100px]">
              <div className="w-8 h-8 flex items-center justify-center">
                <Image
                  src={step.iconSrc}
                  alt={step.name}
                  width={32}
                  height={32}
                  sizes="32px"
                  className={`object-contain ${
                    step.id === currentStep ? "" : "filter grayscale opacity-70"
                  }`}
                />
              </div>
            </div>

            {/* Connector Line */}
            {index < steps.length - 1 && (
              <div className="flex-1 h-0 border-t-2 border-dashed border-[#95929E]"></div>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Labels Row */}
      <div className="flex items-center">
        {steps.map((step, index) => (
          <React.Fragment key={`label-${step.id}`}>
            {/* Label Container */}
            <div className="flex justify-center w-[100px]">
              <span
                className={`font-medium text-[14px] leading-[17px] font-inter text-center whitespace-nowrap ${
                  step.id === currentStep ? "text-[#391460]" : "text-[#95929E]"
                }`}
              >
                {step.name}
              </span>
            </div>

            {/* Spacer for connector line */}
            {index < steps.length - 1 && <div className="flex-1"></div>}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
