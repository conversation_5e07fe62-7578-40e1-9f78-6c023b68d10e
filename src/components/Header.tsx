import Image from "next/image";

interface HeaderProps {
  className?: string;
  onBackToStart?: () => void;
}

export default function Header({ className = "", onBackToStart }: HeaderProps) {
  return (
    <header
      className={`bg-[#391460] px-10 py-2.5 flex items-center justify-center h-[70px] ${className}`}
    >
      <button
        onClick={onBackToStart}
        className="w-[106px] h-[50px] relative cursor-pointer hover:opacity-80 transition-opacity"
      >
        <Image
          src="/acu-logo.png"
          alt="Australian Catholic University"
          fill
          sizes="106px"
          className="object-contain"
        />
      </button>
    </header>
  );
}
