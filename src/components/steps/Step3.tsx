"use client";

import ProgressIndicator from "@/components/ProgressIndicator";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useInterests } from "@/lib/store";
import { useToastValidation } from "@/lib/use-toast-validation";
import { validateStep3 } from "@/lib/validation";
import Image from "next/image";
import { useState } from "react";

interface Step3Props {
  onNext: () => void;
  onBack: () => void;
}

const interestOptions = [
  "Music",
  "Movies",
  "Sports",
  "Gaming",
  "Books",
  "Volunteering",
  "Travel",
  "Podcasts",
  "Technology",
  "AI",
];

export default function Step3({ onNext, onBack }: Step3Props) {
  const { selectedInterests, termsAccepted, setInterests, setTermsAccepted } =
    useInterests();
  const { showValidationErrors, clearErrors } = useToastValidation();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const toggleInterest = (interest: string) => {
    const newInterests = selectedInterests.includes(interest)
      ? selectedInterests.filter((item) => item !== interest)
      : [...selectedInterests, interest];
    setInterests(newInterests);
    // Clear errors when user makes selection
    clearErrors();
  };

  const handleTermsChange = (checked: boolean) => {
    setTermsAccepted(checked);
    // Clear errors when user accepts terms
    clearErrors();
  };

  const handleNext = () => {
    const validation = validateStep3({
      selectedInterests,
      termsAccepted,
    });

    if (validation.success) {
      clearErrors();
      onNext();
    } else {
      showValidationErrors(validation.errors);
    }
  };

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background Image */}
      <div className="absolute top-0 left-0 right-0 h-1/2 z-0">
        <Image
          src="/step1-background.webp"
          alt="Background"
          fill
          sizes="100vw"
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/35"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center h-full pt-[60px] pb-[60px]">
        <Card className="bg-white/80 backdrop-blur-[10px] border-none w-[1100px] h-[560px] relative">
          <CardContent className="p-[40px] flex flex-col items-center gap-[30px] h-full">
            {/* Section 1: Title */}
            <div className="flex flex-col items-center gap-1">
              <h1 className="text-black font-semibold text-[36px] leading-[50px] tracking-[-0.02em] font-montserrat text-center italic">
                Tell Us What You Like
              </h1>
              <p className="text-[#64748B] font-medium text-[18px] leading-[22px] font-montserrat text-center">
                And Your Superpower
              </p>
            </div>

            {/* Section 2: Progress Indicator */}
            <ProgressIndicator currentStep={3} />

            {/* Section 3: Interests Selection */}
            <div className="flex flex-col gap-[40px] w-full">
              <div className="flex flex-col gap-5 w-full max-w-[900px] mx-auto">
                {/* Label */}
                <Label className="text-[#191D23] font-medium text-base leading-[19px] font-inter">
                  My Interests<span className="text-red-500">*</span>
                </Label>

                {/* Multi-select Tags */}
                <div className="flex flex-wrap gap-5">
                  {interestOptions.map((interest) => {
                    const isSelected = selectedInterests.includes(interest);
                    return (
                      <button
                        key={interest}
                        onClick={() => toggleInterest(interest)}
                        className={`
                          flex items-center gap-2 px-6 py-2 rounded-full border transition-colors font-inter text-base font-medium
                          ${
                            isSelected
                              ? "bg-[#D7D0DF] text-[#391460] border-[#D7D0DF]"
                              : "bg-white text-black border-[#A0ABBB] hover:border-[#391460]"
                          }
                        `}
                      >
                        <span>{interest}</span>
                        {isSelected && (
                          <svg
                            width="19"
                            height="19"
                            viewBox="0 0 19 19"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-shrink-0"
                          >
                            <path
                              d="M14.25 4.75L4.75 14.25M4.75 4.75L14.25 14.25"
                              stroke="#391460"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Terms and Conditions Checkbox */}
              <div className="flex items-start gap-[10px] w-full max-w-[900px] mx-auto">
                <div className="flex-shrink-0">
                  <input
                    type="checkbox"
                    id="terms-checkbox"
                    checked={termsAccepted}
                    onChange={(e) => handleTermsChange(e.target.checked)}
                    className="w-6 h-6 border border-[#95929E] rounded-md bg-white checked:bg-[#391460] checked:border-[#391460] focus:ring-2 focus:ring-[#391460]/20"
                  />
                </div>
                <label
                  htmlFor="terms-checkbox"
                  className="text-black font-normal text-sm leading-[1.4] font-inter cursor-pointer"
                >
                  I understand that my information will be used to personalise
                  my career recommendations and for analytics purposes. I agree
                  to the{" "}
                  <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogTrigger asChild>
                      <button
                        type="button"
                        className="text-black underline hover:text-[#391460] transition-colors"
                        onClick={() => setIsDialogOpen(true)}
                      >
                        Terms & Conditions and Privacy Policy
                      </button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh]">
                      <DialogHeader>
                        <DialogTitle className="font-semibold text-base mb-2">
                          Terms & Conditions and Privacy Policy
                        </DialogTitle>
                      </DialogHeader>
                      <ScrollArea className="h-[60vh] pr-4">
                        <div className="space-y-4 text-sm">
                          <div>
                            <p className="mb-2">
                              <strong>Effective Date:</strong> 21st May 2025
                            </p>
                            <p className="mb-4">
                              <strong>Provider:</strong> What&apos;s On! Campus
                              Pty Ltd via Australian Catholic University (ACU),
                              Sydney
                            </p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">1. Overview</h4>
                            <p className="mb-4">
                              By participating in the What&apos;s On! Passport
                              experience at the ACU Recruitment Event, you
                              (&quot;User&quot;) agree to the terms below. If
                              you are under 18 years of age, you must have
                              permission from a parent or guardian to use the
                              product.
                            </p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              2. Eligibility
                            </h4>
                            <p className="mb-2">
                              The service is intended for high school students
                              attending the ACU Open Day. By using the service,
                              you confirm that:
                            </p>
                            <ul className="list-disc list-inside mb-4 space-y-1">
                              <li>
                                You are at least 15 years of age, or have
                                received parental consent.
                              </li>
                              <li>
                                You understand this is a career exploration and
                                educational tool.
                              </li>
                            </ul>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              3. Product Use
                            </h4>
                            <p className="mb-2">
                              The What&apos;s On! Passport (AI Career Coach)
                              allows students to:
                            </p>
                            <ul className="list-disc list-inside mb-2 space-y-1">
                              <li>
                                Interact with a digital AI Coach using natural
                                language.
                              </li>
                              <li>
                                Receive career guidance based on input (e.g.,
                                interests and hobbies).
                              </li>
                              <li>
                                Access a personalised digital &quot;career
                                snapshot&quot; or &quot;career roadmap&quot;.
                              </li>
                            </ul>
                            <p className="mb-4">
                              You agree to use the product only for lawful,
                              educational purposes and not to misuse or disrupt
                              the service.
                            </p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              4. Data Collection and Use
                            </h4>
                            <p className="mb-2">
                              We collect the following data during your
                              interaction:
                            </p>
                            <ul className="list-disc list-inside mb-2 space-y-1">
                              <li>Your shared interests and hobbies</li>
                              <li>Questions you ask the AI</li>
                              <li>
                                Your general feedback and engagement metrics
                              </li>
                            </ul>
                            <p className="mb-2">This data:</p>
                            <ul className="list-disc list-inside mb-4 space-y-1">
                              <li>
                                Is used to improve our services and provide you
                                with a personalised experience.
                              </li>
                              <li>
                                Will be anonymised before any reports are shared
                                with ACU.
                              </li>
                              <li>
                                Is not used to make any academic or admissions
                                decisions.
                              </li>
                            </ul>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              5. Privacy and Data Protection
                            </h4>
                            <p className="mb-2">
                              We take your privacy seriously:
                            </p>
                            <ul className="list-disc list-inside mb-2 space-y-1">
                              <li>
                                We do not collect your full name, school name,
                                contact details, or any sensitive identifiers
                                unless explicitly provided by you.
                              </li>
                              <li>
                                All data is securely stored on Australian
                                servers and handled in accordance with the
                                Privacy Act 1988 (Cth).
                              </li>
                              <li>
                                Data may be shared in aggregate (anonymised)
                                format with ACU to improve their course
                                communication and outreach.
                              </li>
                            </ul>
                            <p className="mb-2">You have the right to:</p>
                            <ul className="list-disc list-inside mb-2 space-y-1">
                              <li>
                                Request to see what data we collected during
                                your use.
                              </li>
                              <li>
                                Request deletion of your data after the event.
                              </li>
                            </ul>
                            <p className="mb-4">
                              For any privacy queries, contact:
                              <EMAIL>
                            </p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              6. Intellectual Property
                            </h4>
                            <p className="mb-4">
                              All intellectual property associated with the
                              What&apos;s On! Passport belongs to What&apos;s
                              On! Campus Pty Ltd. You may not copy,
                              reverse-engineer, or reproduce any part of the
                              experience or materials provided.
                            </p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              7. Limitation of Liability
                            </h4>
                            <p className="mb-4">
                              This is an educational tool. The advice and
                              suggestions given by the AI Career Coach are
                              general in nature and not a substitute for formal
                              academic or career counseling. We are not liable
                              for any decisions made based on this experience.
                            </p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              8. Contact Details
                            </h4>
                            <p className="mb-2">
                              For support, feedback, or questions:
                            </p>
                            <p className="mb-1">
                              What&apos;s On! Campus Pty Ltd
                            </p>
                            <p className="mb-1">Email: <EMAIL></p>
                            <p className="mb-4">Phone: +61 416 876 876</p>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">
                              9. Changes to Terms
                            </h4>
                            <p className="mb-4">
                              We may update these Terms & Conditions from time
                              to time. Any changes will be posted and made
                              effective immediately.
                            </p>
                          </div>
                        </div>
                      </ScrollArea>
                    </DialogContent>
                  </Dialog>
                  .*
                </label>
              </div>
            </div>
          </CardContent>

          {/* Fixed position buttons */}
          <Button
            onClick={onBack}
            variant="outline"
            className="absolute bottom-[20px] left-[20px] bg-white text-[#391460] border-[#391460] rounded-[8px] px-5 py-2 flex items-center gap-4 font-medium text-base leading-[19px] font-inter hover:bg-[#391460]/10"
          >
            <Image
              src="/start-arrow-icon.png"
              alt="Back Arrow"
              width={26}
              height={26}
            />
            <span>Back</span>
          </Button>
          <Button
            onClick={handleNext}
            className="absolute bottom-[20px] right-[20px] bg-[#391460] text-white rounded-[8px] px-5 py-2 flex items-center gap-4 font-medium text-base leading-[19px] font-inter hover:bg-[#391460]/90"
          >
            <span>Next</span>
            <Image
              src="/start-arrow-icon-secondary.png"
              alt="Start Arrow"
              width={26}
              height={26}
            />
          </Button>
        </Card>
      </div>
    </div>
  );
}
