import { Button } from "@/components/ui/button";
import Image from "next/image";

interface Step0Props {
  onNext: () => void;
}

export default function Step0({ onNext }: Step0Props) {
  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/landing.webp"
          alt="Background"
          fill
          sizes="100vw"
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/36"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-20">
          {/* Text Content */}
          <div className="flex flex-col gap-4 items-center">
            <h1 className="text-white font-bold text-5xl leading-[1.22] tracking-[-0.02em] font-montserrat italic w-[1030px] max-w-full text-center">
              Unlock Your Dream Job With AI
            </h1>
            <p className="text-[#F7F8F9] font-semibold text-2xl leading-[1.22] font-montserrat text-center">
              Create Your Personalised Pathway
            </p>
          </div>

          {/* CTA Button */}
          <Button
            onClick={onNext}
            variant="default"
            size="lg"
            className="bg-[#391460] text-white px-[60px] py-2 rounded-lg font-bold text-base leading-[1.21] font-inter hover:bg-[#391460]/90 transition-colors flex items-center gap-4"
          >
            <span>Start</span>
            <Image
              src="/start-arrow-icon-secondary.png"
              alt="Start Arrow"
              width={26}
              height={26}
            />
          </Button>
        </div>
      </div>
    </div>
  );
}
