"use client";

import ProgressIndicator from "@/components/ProgressIndicator";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useFormStore, useNavigation, usePersonalInfo } from "@/lib/store";
import Image from "next/image";

interface Step6Props {
  onFinish: () => void;
}

export default function Step6({ onFinish }: Step6Props) {
  const { firstName, email } = usePersonalInfo();
  const { finishFlow } = useNavigation();
  const qrCode = useFormStore((state) => state.qrCode);

  const displayName = firstName || "Student";
  const displayEmail = email || "your email";

  const handleFinish = () => {
    finishFlow(); // Reset all data for next user
    onFinish();
  };

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background Image */}
      <div className="absolute top-0 left-0 right-0 h-1/2 z-0">
        <Image
          src="/step1-background.webp"
          alt="Background"
          fill
          sizes="100vw"
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/35"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center h-full pt-[60px] pb-[60px]">
        <Card className="bg-white/80 backdrop-blur-[10px] border-none w-[1100px] h-[560px] relative">
          <CardContent className="p-[40px] flex flex-col items-center gap-[30px] h-full">
            {/* Section 1: Title */}
            <div className="flex flex-col items-center gap-1">
              <h1 className="text-black font-semibold text-[36px] leading-[50px] tracking-[-0.02em] font-montserrat text-center italic">
                We are all set, {displayName}!
              </h1>
              <p className="text-[#64748B] font-medium text-[18px] leading-[22px] font-montserrat text-center">
                Your Personalised Career Roadmap Is Being Sent To {displayEmail}
              </p>
            </div>

            {/* Section 2: Progress Indicator */}
            <ProgressIndicator currentStep={5} />

            {/* Section 3: QR Code */}
            <div className="flex flex-col gap-[40px] w-full">
              <div className="flex flex-col items-center gap-1">
                <div className="w-[180px] h-[180px] mx-auto flex items-center justify-center">
                  {qrCode ? (
                    <div
                      className="w-[180px] h-[180px] flex items-center justify-center"
                      dangerouslySetInnerHTML={{ __html: qrCode }}
                    />
                  ) : (
                    <div className="w-[180px] h-[180px] bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center rounded-lg">
                      <div className="text-center">
                        <div className="w-8 h-8 mx-auto mb-2 animate-spin">
                          <svg
                            className="w-8 h-8 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                            />
                          </svg>
                        </div>
                        <p className="text-sm text-gray-500">
                          Generating QR Code...
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                <p className="text-[#191D23] font-medium text-base leading-[19px] font-montserrat text-center">
                  Scan to view your career roadmap
                </p>
              </div>
            </div>
          </CardContent>

          {/* Fixed position finish button */}
          <Button
            onClick={handleFinish}
            className="absolute bottom-[20px] left-1/2 transform -translate-x-1/2 w-[180px] bg-[#391460] text-white rounded-[8px] px-5 py-2 flex items-center justify-center font-medium text-base leading-[19px] font-inter hover:bg-[#391460]/90"
          >
            <span>Finish</span>
          </Button>
        </Card>
      </div>
    </div>
  );
}
