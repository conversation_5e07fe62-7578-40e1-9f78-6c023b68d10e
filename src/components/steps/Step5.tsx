"use client";

import FeedbackModal from "@/components/FeedbackModal";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useUserCreation } from "@/lib/hooks/useUserCreation";
import {
  useCareerPath,
  useDreamJob,
  useFormStore,
  useInitialQuestion,
  useInterests,
  usePersonalInfo,
} from "@/lib/store";
import { useChat } from "@ai-sdk/react";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

interface Step5Props {
  onNext: () => void;
}

export default function Step5({ onNext }: Step5Props) {
  const { firstName, lastName, email, highSchool } = usePersonalInfo();
  const { dreamRole, dreamCompany } = useDreamJob();
  const { selectedInterests } = useInterests();
  const { initialQuestion, setInitialQuestion } = useInitialQuestion();
  const userId = useFormStore((state) => state.userId);
  const { createUser, isLoading: isCreatingUser } = useUserCreation();
  const { isGeneratingCareerPath, careerPathCompleted, careerPathError } =
    useCareerPath();
  const setFeedbackModalOpen = useFormStore(
    (state) => state.setFeedbackModalOpen,
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [previousMessageCount, setPreviousMessageCount] = useState(0);
  const hasInitialQuestionSentRef = useRef(false);

  // Prepare user info for API
  const userInfo = {
    firstName,
    lastName,
    email,
    highSchool,
    dreamRole,
    dreamCompany,
    selectedInterests,
  };

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit: originalHandleSubmit,
    isLoading,
    error,
    append,
  } = useChat({
    api: "/api/chat",
    initialMessages: [],
    body: {
      userInfo,
    },
  });

  // Function to save questions to database
  const saveQuestion = React.useCallback(
    async (userId: string, question: string) => {
      if (!question.trim()) return;

      try {
        const response = await fetch("/api/users/save-question", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: userId,
            first_name: firstName,
            last_name: lastName,
            question: question,
          }),
        });

        const result = await response.json();
        if (!response.ok) {
          console.error("Failed to save question:", result.error);
        } else {
          console.log("Question saved successfully");
        }
      } catch (error) {
        console.error("Error saving question:", error);
      }
    },
    [firstName, lastName],
  );

  // Custom handleSubmit that saves questions
  const handleSubmit = async (e?: React.FormEvent<HTMLFormElement>) => {
    if (e) e.preventDefault();

    // Save the question before sending it to AI
    if (userId && input.trim()) {
      await saveQuestion(userId, input.trim());
    }

    // Call the original handleSubmit
    originalHandleSubmit(e);
  };

  // Auto-scroll to bottom when messages are added or updated (including streaming responses)
  useEffect(() => {
    const currentMessageCount = messages.length;

    // Scroll when new messages are added or when messages are being updated (streaming)
    if (currentMessageCount > previousMessageCount || isLoading) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);
      setPreviousMessageCount(currentMessageCount);
    }
  }, [messages.length, previousMessageCount, isLoading]);

  // Auto-scroll when message content changes (for streaming responses)
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 50);
    }
  }, [messages]);

  // Send initial question when component mounts if there is one
  useEffect(() => {
    if (
      initialQuestion &&
      initialQuestion.trim() &&
      !hasInitialQuestionSentRef.current
    ) {
      // Save the initial question if user exists
      if (userId) {
        saveQuestion(userId, initialQuestion.trim());
      }

      // Use append to send the user's question
      append({
        role: "user",
        content: initialQuestion,
      });

      // Clear the initial question after using it
      setInitialQuestion("");
      hasInitialQuestionSentRef.current = true;
    }
  }, [
    initialQuestion,
    append,
    setInitialQuestion,
    userId,
    firstName,
    lastName,
    saveQuestion,
  ]);

  // Show toast notification when career path is completed
  useEffect(() => {
    if (careerPathCompleted) {
      toast.success("🎉 Your Career Roadmap is Ready!", {
        description:
          "Your personalized career path has been generated. Click 'View Your Career Roadmap' to see it!",
        duration: 5000,
      });
    }
  }, [careerPathCompleted]);

  const handleNext = async () => {
    try {
      // If user already exists and career path is completed, open feedback modal
      if (userId && careerPathCompleted) {
        setFeedbackModalOpen(true);
        return;
      }

      // If user exists but career path is still generating, just proceed
      if (userId && isGeneratingCareerPath) {
        onNext();
        return;
      }

      // If there's an error, still proceed but log it
      if (careerPathError) {
        console.error("Career path generation error:", careerPathError);
        // Still proceed for now (in production, you might want to show an error)
      }

      // If no user ID exists, create user manually (fallback)
      if (!userId) {
        const newUserId = await createUser({
          firstName,
          lastName,
          email,
          highSchool,
          dreamRole,
          dreamCompany,
          selectedInterests,
        });

        if (newUserId) {
          useFormStore.getState().setUserId(newUserId);
          console.log("User created with ID:", newUserId);

          // Save the initial question if there was one
          if (initialQuestion && initialQuestion.trim()) {
            await saveQuestion(newUserId, initialQuestion);
          }
        } else {
          console.error("Failed to create user");
        }
      }

      // Proceed to next step
      onNext();
    } catch (error) {
      console.error("Error in handleNext:", error);
      // Still proceed for now
      onNext();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background Image */}
      <div className="absolute top-0 left-0 right-0 h-1/2 z-0">
        <Image
          src="/step1-background.webp"
          alt="Background"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/35"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center h-full pt-[60px] pb-[60px]">
        <Card className="bg-white/80 backdrop-blur-[10px] border-none w-[1100px] h-[560px] relative">
          <CardContent className="p-0 h-full flex flex-col">
            {/* Header Section */}
            <div className="flex items-center justify-between p-[20px] border-b border-[#E2E8F0]">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 relative">
                  <Image
                    src="/progress-indicator/ai-coach.png"
                    alt="AI Coach"
                    fill
                    sizes="32px"
                    className="object-contain"
                  />
                </div>
                <h1 className="text-black font-semibold text-[24px] leading-[30px] tracking-[-0.02em] font-montserrat">
                  AI Coach
                </h1>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={handleNext}
                  disabled={
                    isCreatingUser ||
                    (isGeneratingCareerPath && !careerPathCompleted)
                  }
                  className="bg-[#391460] text-white rounded-[8px] px-4 py-2 flex items-center gap-3 font-medium text-sm leading-[17px] font-inter hover:bg-[#391460]/90 disabled:opacity-50"
                >
                  <span>
                    {isCreatingUser
                      ? "Creating User..."
                      : isGeneratingCareerPath && !careerPathCompleted
                        ? "Generating Career Path..."
                        : careerPathCompleted
                          ? "View Your Career Roadmap"
                          : "Get Your Career Roadmap"}
                  </span>
                  {!isCreatingUser &&
                    !(isGeneratingCareerPath && !careerPathCompleted) && (
                      <div className="w-5 h-5 relative">
                        <Image
                          src="/mail.png"
                          alt="Career Roadmap"
                          fill
                          sizes="20px"
                          className="object-contain"
                        />
                      </div>
                    )}
                </Button>
              </div>
            </div>

            {/* Chat Messages Section */}
            <div className="flex-1 overflow-y-auto p-[20px] space-y-4">
              {error && (
                <div className="bg-red-50 text-red-600 p-3 rounded-lg text-sm">
                  Sorry, there was an error connecting to the AI Coach. Please
                  try again.
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`max-w-[70%] rounded-lg p-3 ${
                      message.role === "user"
                        ? "bg-[#391460] text-white"
                        : "bg-[#F1F5F9] text-black"
                    }`}
                  >
                    <p className="text-sm leading-[1.4] font-inter whitespace-pre-wrap">
                      {message.content}
                    </p>
                  </div>
                </div>
              ))}

              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-[#F1F5F9] text-black max-w-[70%] rounded-lg p-3">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div
                        className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                        style={{ animationDelay: "0.1s" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Invisible div to scroll to */}
              <div ref={messagesEndRef} />
            </div>

            {/* Input Section */}
            <div className="border-t border-[#E2E8F0] p-[20px]">
              <form onSubmit={handleSubmit} className="flex items-center gap-3">
                <Input
                  value={input}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your message here..."
                  className="flex-1 h-[48px] bg-white border border-[#A0ABBB] rounded-[10px] px-4 text-base leading-[19px] font-inter placeholder:text-[#64748B]"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading || !input.trim()}
                  className="w-[48px] h-[48px] bg-[#391460] text-white rounded-[10px] flex items-center justify-center hover:bg-[#391460]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 2L9 11M18 2L12 18L9 11M18 2L2 8L9 11"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </form>
            </div>
          </CardContent>
        </Card>
      </div>
      <FeedbackModal onFeedbackSubmit={onNext} />
    </div>
  );
}
