"use client";

import ProgressIndicator from "@/components/ProgressIndicator";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { usePersonalInfo } from "@/lib/store";
import { useToastValidation } from "@/lib/use-toast-validation";
import { validateStep1 } from "@/lib/validation";
import Image from "next/image";

interface Step1Props {
  onNext: () => void;
  onBack: () => void;
}

export default function Step1({ onNext, onBack }: Step1Props) {
  const { firstName, lastName, email, highSchool, setPersonalInfo } =
    usePersonalInfo();
  const { showValidationErrors, clearErrors } = useToastValidation();

  const handleInputChange = (field: string, value: string) => {
    setPersonalInfo({ [field]: value });
    // Clear errors when user starts typing
    clearErrors();
  };

  const handleNext = () => {
    const validation = validateStep1({
      firstName,
      lastName,
      email,
      highSchool,
    });

    if (validation.success) {
      clearErrors();
      onNext();
    } else {
      showValidationErrors(validation.errors);
    }
  };

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background Image */}
      <div className="absolute top-0 left-0 right-0 h-1/2 z-0">
        <Image
          src="/step1-background.webp"
          alt="Background"
          fill
          sizes="100vw"
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/35"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center h-full pt-[60px] pb-[60px]">
        <Card className="bg-white/80 backdrop-blur-[10px] border-none w-[1100px] h-[560px] relative">
          <CardContent className="p-[40px] flex flex-col items-center gap-[30px] h-full">
            {/* Section 1: Title */}
            <div className="flex flex-col items-center gap-1">
              <h1 className="text-black font-semibold text-[36px] leading-[50px] tracking-[-0.02em] font-montserrat text-center italic">
                Tell Us Who You Are
              </h1>
              <p className="text-[#64748B] font-medium text-[18px] leading-[22px] font-montserrat text-center">
                Some Basic Details To Start With
              </p>
            </div>

            {/* Section 2: Progress Indicator */}
            <ProgressIndicator currentStep={1} />

            {/* Section 3: Form */}
            <div className="flex flex-col gap-[60px] w-full">
              <div className="flex flex-col gap-[36px] w-full">
                {/* First Row - First Name & Last Name */}
                <div className="flex gap-5 w-full justify-center">
                  <div className="flex flex-col gap-3">
                    <Label className="text-black font-medium text-base leading-[19px] font-inter">
                      First Name<span className="text-red-500">*</span>
                    </Label>
                    <Input
                      value={firstName}
                      onChange={(e) =>
                        handleInputChange("firstName", e.target.value)
                      }
                      placeholder="First Name"
                      className="w-[440px] h-[56px] bg-white border border-[#A0ABBB] rounded-[10px] px-[18px] py-6 text-base leading-[19px] font-inter placeholder:text-[#64748B]"
                    />
                  </div>
                  <div className="flex flex-col gap-3">
                    <Label className="text-black font-medium text-base leading-[19px] font-inter">
                      Last Name<span className="text-red-500">*</span>
                    </Label>
                    <Input
                      value={lastName}
                      onChange={(e) =>
                        handleInputChange("lastName", e.target.value)
                      }
                      placeholder="Last Name"
                      className="w-[440px] h-[56px] bg-white border border-[#A0ABBB] rounded-[10px] px-[18px] py-6 text-base leading-[19px] font-inter placeholder:text-[#64748B]"
                    />
                  </div>
                </div>

                {/* Second Row - Email & High School */}
                <div className="flex gap-5 w-full justify-center">
                  <div className="flex flex-col gap-3">
                    <Label className="text-black font-medium text-base leading-[19px] font-inter">
                      Email<span className="text-red-500">*</span>
                    </Label>
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      placeholder="Email"
                      className="w-[440px] h-[56px] bg-white border border-[#A0ABBB] rounded-[10px] px-[18px] py-6 text-base leading-[19px] font-inter placeholder:text-[#64748B]"
                    />
                  </div>
                  <div className="flex flex-col gap-3">
                    <Label className="text-black font-medium text-base leading-[19px] font-inter">
                      High School<span className="text-red-500">*</span>
                    </Label>
                    <Input
                      value={highSchool}
                      onChange={(e) =>
                        handleInputChange("highSchool", e.target.value)
                      }
                      placeholder="High School Name"
                      className="w-[440px] h-[56px] bg-white border border-[#A0ABBB] rounded-[10px] px-[18px] py-6 text-base leading-[19px] font-inter placeholder:text-[#64748B]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          {/* Fixed position buttons */}
          <Button
            onClick={onBack}
            variant="outline"
            className="absolute bottom-[20px] left-[20px] bg-white text-[#391460] border-[#391460] rounded-[8px] px-5 py-2 flex items-center gap-4 font-medium text-base leading-[19px] font-inter hover:bg-[#391460]/10"
          >
            <Image
              src="/start-arrow-icon.png"
              alt="Back Arrow"
              width={26}
              height={26}
            />
            <span>Back</span>
          </Button>
          <Button
            onClick={handleNext}
            className="absolute bottom-[20px] right-[20px] bg-[#391460] text-white rounded-[8px] px-5 py-2 flex items-center gap-4 font-medium text-base leading-[19px] font-inter hover:bg-[#391460]/90"
          >
            <span>Next</span>
            <Image
              src="/start-arrow-icon-secondary.png"
              alt="Start Arrow"
              width={26}
              height={26}
            />
          </Button>
        </Card>
      </div>
    </div>
  );
}
