"use client";

import ProgressIndicator from "@/components/ProgressIndicator";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useUserCreation } from "@/lib/hooks/useUserCreation";
import {
  useDreamJob,
  useFormStore,
  useInitialQuestion,
  useInterests,
  usePersonalInfo,
} from "@/lib/store";
import Image from "next/image";
import { useState } from "react";

const suggestedQuestions = [
  "What courses will I be taking",
  "What I will learn from this degree",
  "What career opportunities will I unlock",
  "How to get more involved",
  "Why a double degree is better",
  "How much does a graduate with this degree earn",
];

export default function Step4() {
  const { firstName, lastName, email, highSchool } = usePersonalInfo();
  const { dreamRole, dreamCompany } = useDreamJob();
  const { selectedInterests } = useInterests();
  const { setInitialQuestion } = useInitialQuestion();
  const { createUserAndGenerateCareerPath } = useUserCreation();
  const { setCurrentStep } = useFormStore();
  const [customQuestion, setCustomQuestion] = useState("");

  const displayName = firstName || "Student";

  // Prepare user info for background processing
  const userInfo = {
    firstName,
    lastName,
    email,
    highSchool,
    dreamRole,
    dreamCompany,
    selectedInterests,
  };

  const handleQuestionClick = async (question: string) => {
    setInitialQuestion(question);

    // Navigate to Step 5
    setCurrentStep(5);

    // Start background generation asynchronously (don't wait)
    createUserAndGenerateCareerPath(userInfo, question).catch((error) => {
      console.error("Error starting background generation:", error);
    });
  };

  const handleSendCustomQuestion = async () => {
    if (customQuestion.trim()) {
      setInitialQuestion(customQuestion);

      // Navigate to Step 5
      setCurrentStep(5);

      // Start background generation asynchronously (don't wait)
      createUserAndGenerateCareerPath(userInfo, customQuestion).catch(
        (error) => {
          console.error("Error starting background generation:", error);
        },
      );
    }
  };

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background Image */}
      <div className="absolute top-0 left-0 right-0 h-1/2 z-0">
        <Image
          src="/step1-background.webp"
          alt="Background"
          fill
          sizes="100vw"
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/35"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center h-full pt-[60px] pb-[60px]">
        <Card className="bg-white/80 backdrop-blur-[10px] border-none w-[1100px] h-[560px] relative">
          <CardContent className="p-0 h-full flex flex-col">
            {/* Main Content Area (Step 3 style) */}
            <div className="flex-1 overflow-y-auto p-[40px] flex flex-col items-center gap-[30px]">
              {/* Section 1: Title */}
              <div className="flex flex-col items-center gap-1">
                <h1 className="text-black font-semibold text-[36px] leading-[50px] tracking-[-0.02em] font-montserrat text-center italic">
                  Hey {displayName}, Let&apos;s Get Started!
                </h1>
                <p className="text-[#64748B] font-medium text-[18px] leading-[22px] font-montserrat text-center">
                  Talk To The AI Coach
                </p>
              </div>

              {/* Section 2: Progress Indicator */}
              <ProgressIndicator currentStep={4} />

              {/* Section 3: AI Coach Interface */}
              <div className="flex flex-col gap-[40px] w-full">
                <div className="flex flex-col gap-3 w-full max-w-[980px] mx-auto">
                  {/* Help me know label */}
                  <h3 className="text-[#391460] font-medium text-lg leading-[22px] font-inter">
                    Help me know…
                  </h3>

                  {/* Suggested Questions */}
                  <div className="flex flex-wrap gap-3">
                    {suggestedQuestions.map((question, index) => (
                      <button
                        key={index}
                        onClick={() => handleQuestionClick(question)}
                        className="bg-white border border-[#A0ABBB] rounded-xl px-3 py-2 text-[#64748B] font-medium text-sm leading-[17px] font-inter hover:border-[#391460] hover:text-[#391460] transition-colors"
                      >
                        {question}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Input Section (Step 5 style) */}
            <div className="border-t border-[#E2E8F0] p-[20px]">
              <div className="flex items-center gap-3">
                <Input
                  value={customQuestion}
                  onChange={(e) => setCustomQuestion(e.target.value)}
                  placeholder="How can I help you today?"
                  className="flex-1 h-[48px] bg-white border border-[#A0ABBB] rounded-[10px] px-4 text-base leading-[19px] font-inter placeholder:text-[#64748B]"
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      handleSendCustomQuestion();
                    }
                  }}
                />
                <button
                  onClick={handleSendCustomQuestion}
                  disabled={!customQuestion.trim()}
                  className="w-[48px] h-[48px] bg-[#391460] text-white rounded-[10px] flex items-center justify-center hover:bg-[#391460]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 2L9 11M18 2L12 18L9 11M18 2L2 8L9 11"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
