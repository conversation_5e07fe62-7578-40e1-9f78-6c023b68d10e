"use client";

import ProgressIndicator from "@/components/ProgressIndicator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDreamJob } from "@/lib/store";
import { useToastValidation } from "@/lib/use-toast-validation";
import { validateStep2 } from "@/lib/validation";
import Image from "next/image";

interface Step2Props {
  onNext: () => void;
  onBack: () => void;
}

const dreamRoles = [
  "Company Founder",
  "AI & Data Engineer",
  "Software Developer",
  "Cybersecurity Specialist",
  "Systems Architect",
  "UX Designer",
  "IT Manager",
  "Data Analyst",
  "Game Developer",
  "Hardware Engineer",
];

const companies = [
  "My Own Company",
  "Google",
  "Apple",
  "Microsoft",
  "Tesla",
  "NVIDIA",
  "Amazon",
  "Meta",
  "OpenAI",
  "Netflix",
  "Atlassian",
  "Adobe",
  "NASA",
  "Ubisoft",
  "Epic Games",
  "Intel",
];

export default function Step2({ onNext, onBack }: Step2Props) {
  const { dreamRole, dreamCompany, setDreamJob } = useDreamJob();
  const { showValidationErrors, clearErrors } = useToastValidation();

  const handleSelectChange = (field: string, value: string) => {
    setDreamJob({ [field]: value });
    // Clear errors when user makes selection
    clearErrors();
  };

  const handleNext = () => {
    const validation = validateStep2({
      dreamRole,
      dreamCompany,
    });

    if (validation.success) {
      clearErrors();
      onNext();
    } else {
      showValidationErrors(validation.errors);
    }
  };

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background Image */}
      <div className="absolute top-0 left-0 right-0 h-1/2 z-0">
        <Image
          src="/step1-background.webp"
          alt="Background"
          fill
          sizes="100vw"
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/35"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center h-full pt-[60px] pb-[60px]">
        <Card className="bg-white/80 backdrop-blur-[10px] border-none w-[1100px] h-[560px] relative">
          <CardContent className="p-[40px] flex flex-col items-center gap-[30px] h-full">
            {/* Section 1: Title */}
            <div className="flex flex-col items-center gap-1">
              <h1 className="text-black font-semibold text-[36px] leading-[50px] tracking-[-0.02em] font-montserrat text-center italic">
                Tell Us About Your Dream Job
              </h1>
              <p className="text-[#64748B] font-medium text-[18px] leading-[22px] font-montserrat text-center">
                And Your Dream Company
              </p>
            </div>

            {/* Section 2: Progress Indicator */}
            <ProgressIndicator currentStep={2} />

            {/* Section 3: Form */}
            <div className="flex flex-col gap-[60px] w-full">
              <div className="flex items-center justify-start gap-5 pt-[31px] pl-[80px]">
                {/* I want to become a/an */}
                <Label className="text-[#191D23] font-medium text-base leading-[19px] font-inter">
                  I want to become a/an
                </Label>

                {/* Dream Role Select */}
                <Select
                  value={dreamRole}
                  onValueChange={(value) =>
                    handleSelectChange("dreamRole", value)
                  }
                >
                  <SelectTrigger className="w-[243px] h-[56px] bg-white border border-[#A0ABBB] rounded-[10px] px-4 text-base font-inter">
                    <SelectValue
                      placeholder="Dream Role"
                      className="text-[#64748B]"
                    />
                  </SelectTrigger>
                  <SelectContent
                    className="max-h-[220px] overflow-y-auto"
                    side="bottom"
                    align="start"
                  >
                    {dreamRoles.map((role) => (
                      <SelectItem key={role} value={role}>
                        {role}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* at */}
                <Label className="text-[#191D23] font-medium text-base leading-[19px] font-inter">
                  at
                </Label>

                {/* Dream Company Select */}
                <Select
                  value={dreamCompany}
                  onValueChange={(value) =>
                    handleSelectChange("dreamCompany", value)
                  }
                >
                  <SelectTrigger className="w-[243px] h-[56px] bg-white border border-[#A0ABBB] rounded-[10px] px-4 text-base font-inter">
                    <SelectValue
                      placeholder="Dream Company"
                      className="text-[#64748B]"
                    />
                  </SelectTrigger>
                  <SelectContent
                    className="max-h-[220px] overflow-y-auto"
                    side="bottom"
                    align="start"
                  >
                    {companies.map((company) => (
                      <SelectItem key={company} value={company}>
                        {company}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>

          {/* Fixed position buttons */}
          <Button
            onClick={onBack}
            variant="outline"
            className="absolute bottom-[20px] left-[20px] bg-white text-[#391460] border-[#391460] rounded-[8px] px-5 py-2 flex items-center gap-4 font-medium text-base leading-[19px] font-inter hover:bg-[#391460]/10"
          >
            <Image
              src="/start-arrow-icon.png"
              alt="Back Arrow"
              width={26}
              height={26}
            />
            <span>Back</span>
          </Button>
          <Button
            onClick={handleNext}
            className="absolute bottom-[20px] right-[20px] bg-[#391460] text-white rounded-[8px] px-5 py-2 flex items-center gap-4 font-medium text-base leading-[19px] font-inter hover:bg-[#391460]/90"
          >
            <span>Next</span>
            <Image
              src="/start-arrow-icon-secondary.png"
              alt="Start Arrow"
              width={26}
              height={26}
            />
          </Button>
        </Card>
      </div>
    </div>
  );
}
