"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useFormStore } from "@/lib/store";
import { ThumbsDown, ThumbsUp } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface FeedbackModalProps {
  onFeedbackSubmit: () => void;
}

export default function FeedbackModal({
  onFeedbackSubmit,
}: FeedbackModalProps) {
  const isOpen = useFormStore((state) => state.isFeedbackModalOpen);
  const setOpen = useFormStore((state) => state.setFeedbackModalOpen);
  const storeFeedback = useFormStore((state) => state.careerPathFeedback);
  const setFeedback = useFormStore((state) => state.setCareerPathFeedback);
  const userId = useFormStore((state) => state.userId);
  const firstName = useFormStore((state) => state.firstName);
  const [selectedFeedback, setSelectedFeedback] = useState<
    "helpful" | "not_helpful" | null
  >(null);

  useEffect(() => {
    if (isOpen) {
      setSelectedFeedback(
        storeFeedback === "helpful"
          ? "helpful"
          : storeFeedback === "not_helpful"
            ? "not_helpful"
            : null,
      );
    }
  }, [isOpen, storeFeedback]);

  const handleFeedback = async (feedbackType: "helpful" | "not_helpful") => {
    setSelectedFeedback(feedbackType);
    setFeedback(feedbackType);

    if (userId) {
      console.log(
        "API update: Attempting to update user feedback for userId:",
        userId,
      );
      const feedback_helpful = feedbackType === "helpful";

      try {
        const response = await fetch("/api/users/update-feedback", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: userId,
            feedback_helpful,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          console.error("Error updating user feedback:", result.error);
          if (result.details) {
            console.error("Validation details:", result.details);
          }
          // Show user-friendly error message
          toast.error("Failed to save feedback. Please try again.");
        } else {
          console.log("User feedback updated successfully. Response:", result);
          toast.success("Thank you for your feedback!");
        }
      } catch (error) {
        console.error("Exception when updating user feedback:", error);
        // Show user-friendly error message
        toast.error(
          "Failed to save feedback. Please check your connection and try again.",
        );
      }
    } else {
      console.warn("No userId available, skipping user feedback update.");
    }

    setOpen(false);
    onFeedbackSubmit();
  };

  const onOpenChange = (open: boolean) => {
    if (!open && !selectedFeedback && isOpen) {
      return;
    }
    setOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[425px]"
        onPointerDownOutside={(e) => {
          if (isOpen && !selectedFeedback) e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          if (isOpen && !selectedFeedback) e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            One last thing, {firstName}!
          </DialogTitle>
          <DialogDescription>How was the AI Chatbot?</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex justify-around items-center gap-4">
            <Button
              variant={selectedFeedback === "helpful" ? "default" : "outline"}
              size="lg"
              className={`flex-1 gap-2 items-center transition-all duration-150 ease-in-out transform hover:scale-105 ${selectedFeedback === "helpful" ? "bg-green-500 hover:bg-green-600 text-white" : "border-green-500 text-green-500 hover:bg-green-500/10"}`}
              onClick={() => handleFeedback("helpful")}
            >
              <ThumbsUp className="h-6 w-6" />
              <span>Helpful</span>
            </Button>
            <Button
              variant={
                selectedFeedback === "not_helpful" ? "default" : "outline"
              }
              size="lg"
              className={`flex-1 gap-2 items-center transition-all duration-150 ease-in-out transform hover:scale-105 ${selectedFeedback === "not_helpful" ? "bg-red-500 hover:bg-red-600 text-white" : "border-red-500 text-red-500 hover:bg-red-500/10"}`}
              onClick={() => handleFeedback("not_helpful")}
            >
              <ThumbsDown className="h-6 w-6" />
              <span>Not Helpful</span>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
