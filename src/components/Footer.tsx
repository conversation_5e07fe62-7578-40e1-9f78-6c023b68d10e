import Image from "next/image";

interface FooterProps {
  className?: string;
}

export default function Footer({ className = "" }: FooterProps) {
  return (
    <footer
      className={`bg-[#FADC4A] px-5 py-0.5 backdrop-blur-sm h-[70px] flex items-center ${className}`}
    >
      <div className="flex items-center gap-1">
        <span className="text-black font-medium text-sm leading-[1.21] font-inter">
          AI Degree & Career Coach powered by
        </span>
        <div className="w-[150px] h-[55px] relative">
          <div className="absolute top-1 left-0 w-[120px] h-8">
            <Image
              src="/whatson-logo.png"
              alt="What's On Logo"
              fill
              sizes="120px"
              className="object-contain"
            />
          </div>
        </div>
      </div>
    </footer>
  );
}
