import { z } from "zod";

// Step 1 - Personal Info Schema
export const step1Schema = z.object({
  firstName: z.string().min(1, "First name is required").trim(),
  lastName: z.string().min(1, "Last name is required").trim(),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email"),
  highSchool: z.string().min(1, "High school is required").trim(),
});

// Step 2 - Dream Job Schema
export const step2Schema = z.object({
  dreamRole: z.string().min(1, "Please select your dream role"),
  dreamCompany: z.string().min(1, "Please select your dream company"),
});

// Step 3 - Interests Schema
export const step3Schema = z.object({
  selectedInterests: z
    .array(z.string())
    .min(1, "Please select at least one interest"),
  termsAccepted: z.boolean().refine((val) => val === true, {
    message: "Please accept the terms and conditions",
  }),
});

// Export types
export type Step1Data = z.infer<typeof step1Schema>;
export type Step2Data = z.infer<typeof step2Schema>;
export type Step3Data = z.infer<typeof step3Schema>;

// Validation helper functions
export const validateStep1 = (data: Partial<Step1Data>) => {
  try {
    step1Schema.parse(data);
    return { success: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map((err) => err.message),
      };
    }
    return { success: false, errors: ["Validation failed"] };
  }
};

export const validateStep2 = (data: Partial<Step2Data>) => {
  try {
    step2Schema.parse(data);
    return { success: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map((err) => err.message),
      };
    }
    return { success: false, errors: ["Validation failed"] };
  }
};

export const validateStep3 = (data: Partial<Step3Data>) => {
  try {
    step3Schema.parse(data);
    return { success: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map((err) => err.message),
      };
    }
    return { success: false, errors: ["Validation failed"] };
  }
};
