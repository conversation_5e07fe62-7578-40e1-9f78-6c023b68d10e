"use client";

import { toast } from "sonner";
import { useFormStore } from "./store";

export const useToastValidation = () => {
  const validationErrors = useFormStore((state) => state.validationErrors);
  const setValidationErrors = useFormStore(
    (state) => state.setValidationErrors,
  );
  const clearValidationErrors = useFormStore(
    (state) => state.clearValidationErrors,
  );

  const showValidationErrors = (errors: string[]) => {
    if (errors.length === 0) return;

    setValidationErrors(errors);

    // Create formatted error list
    const errorList = errors.map((error, index) => `${index + 1}. ${error}`);
    const errorMessage = errorList.join("\n");

    toast.error("Please fix the following errors:", {
      description: errorMessage,
      duration: 6000,
      style: {
        whiteSpace: "pre-line",
        maxWidth: "500px",
        color: "#000000",
      },
      className: "text-left",
      descriptionClassName: "text-black",
    });
  };

  const clearErrors = () => {
    clearValidationErrors();
  };

  return {
    validationErrors,
    showValidationErrors,
    clearErrors,
  };
};
