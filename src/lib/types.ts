// Database types for the application
export interface User {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  high_school: string;
  dream_role: string;
  dream_company: string;
  selected_interests: string[];
  career_path?: Record<string, unknown>;
  qr_code?: string;
  feedback_helpful?: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface UserQuestion {
  id: number;
  user_id: string;
  first_name: string;
  last_name: string;
  question: string;
  created_at: Date;
}

// Input types for creating records
export interface CreateUserInput {
  firstName: string;
  lastName: string;
  email: string;
  highSchool: string;
  dreamRole: string;
  dreamCompany: string;
  selectedInterests: string[];
}

export interface CreateUserQuestionInput {
  user_id: string;
  first_name: string;
  last_name: string;
  question: string;
}
