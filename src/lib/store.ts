import { create } from "zustand";

// Simple form data types
interface FormData {
  // User ID
  userId: string;

  // Step 1 - Personal Info
  firstName: string;
  lastName: string;
  email: string;
  highSchool: string;

  // Step 2 - Dream Job
  dreamRole: string;
  dreamCompany: string;

  // Step 3 - Interests
  selectedInterests: string[];
  termsAccepted: boolean;

  // Step 4 - Initial Question
  initialQuestion: string;

  // Career Path Generation Status
  isGeneratingCareerPath: boolean;
  careerPathCompleted: boolean;
  careerPathError: string | null;
  qrCode: string | null;
  careerPathUrl: string | null;

  // Step 5 - AI Chat Messages
  chatMessages: Array<{
    id: number;
    text: string;
    isUser: boolean;
    timestamp: Date;
  }>;

  // Current step tracking
  currentStep: number;

  // Validation errors
  validationErrors: string[];

  // Feedback Modal State
  isFeedbackModalOpen: boolean;
  careerPathFeedback: "helpful" | "not_helpful" | null;
}

interface FormActions {
  // User ID action
  setUserId: (userId: string) => void;

  // Step 1 actions
  setPersonalInfo: (
    info: Partial<
      Pick<FormData, "firstName" | "lastName" | "email" | "highSchool">
    >,
  ) => void;

  // Step 2 actions
  setDreamJob: (
    job: Partial<Pick<FormData, "dreamRole" | "dreamCompany">>,
  ) => void;

  // Step 3 actions
  setInterests: (interests: string[]) => void;
  setTermsAccepted: (accepted: boolean) => void;

  // Step 4 actions
  setInitialQuestion: (question: string) => void;

  // Career Path Generation actions
  setCareerPathGenerating: (isGenerating: boolean) => void;
  setCareerPathCompleted: (completed: boolean) => void;
  setCareerPathError: (error: string | null) => void;
  setCareerPathData: (
    qrCode: string | null,
    careerPathUrl: string | null,
  ) => void;

  // Step 5 actions
  addChatMessage: (message: Omit<FormData["chatMessages"][0], "id">) => void;

  // Navigation actions
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;

  // Reset actions
  resetForm: () => void;

  // Validation actions
  setValidationErrors: (errors: string[]) => void;
  clearValidationErrors: () => void;
  validateAndProceed: () => boolean;

  // Feedback Modal Actions
  setFeedbackModalOpen: (isOpen: boolean) => void;
  setCareerPathFeedback: (feedback: "helpful" | "not_helpful" | null) => void;
}

type FormStore = FormData & FormActions;

const getInitialState = (): FormData => ({
  userId: "",
  firstName: "",
  lastName: "",
  email: "",
  highSchool: "",
  dreamRole: "",
  dreamCompany: "",
  selectedInterests: [],
  termsAccepted: false,
  initialQuestion: "",
  isGeneratingCareerPath: false,
  careerPathCompleted: false,
  careerPathError: null,
  qrCode: null,
  careerPathUrl: null,
  chatMessages: [
    {
      id: 1,
      text: "Hi there! I'm Emma, your AI Coach at ACU. I'm really excited to help you explore amazing career opportunities in tech and data science! ACU's Computer Science and Data Science double degree is NSW's ONLY fast-tracked program where you can earn TWO degrees in just four years - that's a whole year ahead of everyone else! Plus, we've got scholarship opportunities and industry internships to help you succeed. Whether you're curious about coding, working with data, or building the next big app, I'm here to show you how this degree can help you achieve your dreams. What would you like to know about your future in tech?",
      isUser: false,
      timestamp: new Date(),
    },
  ],
  currentStep: 0,
  validationErrors: [],

  // Initial Feedback Modal State
  isFeedbackModalOpen: false,
  careerPathFeedback: null,
});

export const useFormStore = create<FormStore>((set) => ({
  ...getInitialState(),

  // User ID action
  setUserId: (userId) => set({ userId }),

  // Step 1 actions
  setPersonalInfo: (info) => set((state) => ({ ...state, ...info })),

  // Step 2 actions
  setDreamJob: (job) => set((state) => ({ ...state, ...job })),

  // Step 3 actions
  setInterests: (interests) => set({ selectedInterests: interests }),

  setTermsAccepted: (accepted) => set({ termsAccepted: accepted }),

  // Step 4 actions
  setInitialQuestion: (question) => set({ initialQuestion: question }),

  // Step 5 actions
  addChatMessage: (message) =>
    set((state) => ({
      chatMessages: [
        ...state.chatMessages,
        { ...message, id: state.chatMessages.length + 1 },
      ],
    })),

  // Navigation actions
  setCurrentStep: (step) => set({ currentStep: step }),

  nextStep: () =>
    set((state) => ({
      currentStep: Math.min(state.currentStep + 1, 6),
    })),

  prevStep: () =>
    set((state) => ({
      currentStep: Math.max(state.currentStep - 1, 0),
    })),

  // Reset everything for next user - creates fresh state with new timestamp
  resetForm: () => set(getInitialState()),

  // Validation actions
  setValidationErrors: (errors) => set({ validationErrors: errors }),

  clearValidationErrors: () => set({ validationErrors: [] }),

  validateAndProceed: () => {
    // We'll implement validation in components directly
    // This is just a placeholder for now
    set({ validationErrors: [] });
    return true;
  },

  // Career Path Generation actions
  setCareerPathGenerating: (isGenerating) =>
    set({ isGeneratingCareerPath: isGenerating }),
  setCareerPathCompleted: (completed) =>
    set({ careerPathCompleted: completed }),
  setCareerPathError: (error) => set({ careerPathError: error }),
  setCareerPathData: (qrCode, careerPathUrl) => set({ qrCode, careerPathUrl }),

  // Feedback Modal Action Implementations
  setFeedbackModalOpen: (isOpen) => set({ isFeedbackModalOpen: isOpen }),
  setCareerPathFeedback: (feedback) => set({ careerPathFeedback: feedback }),
}));

// Convenience selector hooks - using individual selectors to avoid infinite loops
export const usePersonalInfo = () => {
  const firstName = useFormStore((state) => state.firstName);
  const lastName = useFormStore((state) => state.lastName);
  const email = useFormStore((state) => state.email);
  const highSchool = useFormStore((state) => state.highSchool);
  const setPersonalInfo = useFormStore((state) => state.setPersonalInfo);

  return { firstName, lastName, email, highSchool, setPersonalInfo };
};

export const useDreamJob = () => {
  const dreamRole = useFormStore((state) => state.dreamRole);
  const dreamCompany = useFormStore((state) => state.dreamCompany);
  const setDreamJob = useFormStore((state) => state.setDreamJob);

  return { dreamRole, dreamCompany, setDreamJob };
};

export const useInterests = () => {
  const selectedInterests = useFormStore((state) => state.selectedInterests);
  const termsAccepted = useFormStore((state) => state.termsAccepted);
  const setInterests = useFormStore((state) => state.setInterests);
  const setTermsAccepted = useFormStore((state) => state.setTermsAccepted);

  return { selectedInterests, termsAccepted, setInterests, setTermsAccepted };
};

export const useInitialQuestion = () => {
  const initialQuestion = useFormStore((state) => state.initialQuestion);
  const setInitialQuestion = useFormStore((state) => state.setInitialQuestion);

  return { initialQuestion, setInitialQuestion };
};

export const useChatMessages = () => {
  const chatMessages = useFormStore((state) => state.chatMessages);
  const addChatMessage = useFormStore((state) => state.addChatMessage);

  return { chatMessages, addChatMessage };
};

export const useNavigation = () => {
  const currentStep = useFormStore((state) => state.currentStep);
  const setCurrentStep = useFormStore((state) => state.setCurrentStep);
  const nextStep = useFormStore((state) => state.nextStep);
  const prevStep = useFormStore((state) => state.prevStep);
  const resetForm = useFormStore((state) => state.resetForm);
  const validateAndProceed = useFormStore((state) => state.validateAndProceed);

  // Simple function to finish the flow and reset for next user
  const finishFlow = () => {
    resetForm();
  };

  return {
    currentStep,
    setCurrentStep,
    nextStep,
    prevStep,
    finishFlow,
    validateAndProceed,
  };
};

export const useValidation = () => {
  const validationErrors = useFormStore((state) => state.validationErrors);
  const setValidationErrors = useFormStore(
    (state) => state.setValidationErrors,
  );
  const clearValidationErrors = useFormStore(
    (state) => state.clearValidationErrors,
  );
  const validateAndProceed = useFormStore((state) => state.validateAndProceed);

  return {
    validationErrors,
    setValidationErrors,
    clearValidationErrors,
    validateAndProceed,
  };
};

export const useCareerPath = () => {
  const isGeneratingCareerPath = useFormStore(
    (state) => state.isGeneratingCareerPath,
  );
  const careerPathCompleted = useFormStore(
    (state) => state.careerPathCompleted,
  );
  const careerPathError = useFormStore((state) => state.careerPathError);
  const qrCode = useFormStore((state) => state.qrCode);
  const careerPathUrl = useFormStore((state) => state.careerPathUrl);
  const setCareerPathGenerating = useFormStore(
    (state) => state.setCareerPathGenerating,
  );
  const setCareerPathCompleted = useFormStore(
    (state) => state.setCareerPathCompleted,
  );
  const setCareerPathError = useFormStore((state) => state.setCareerPathError);
  const setCareerPathData = useFormStore((state) => state.setCareerPathData);

  return {
    isGeneratingCareerPath,
    careerPathCompleted,
    careerPathError,
    qrCode,
    careerPathUrl,
    setCareerPathGenerating,
    setCareerPathCompleted,
    setCareerPathError,
    setCareerPathData,
  };
};
