# 🎓 ACU AI Career Coach

An AI-powered career exploration and student engagement platform designed for Australian Catholic University (ACU) recruitment events. This comprehensive web application helps high school students discover their dream careers, create personalized career roadmaps, and engage with AI-powered career coaching through an intuitive 6-step journey.

## ✨ Features

- **Interactive Multi-Step Form**: Guided 6-step process collecting student information, career aspirations, and interests
- **AI Career Coaching**: Advanced AI interface powered by Google Gemini for personalized career guidance and Q&A
- **Personalized Career Roadmaps**: AI-generated custom career recommendations with detailed pathways
- **Email Integration**: Automated welcome emails with career roadmap links sent via Resend
- **QR Code Generation**: Dynamic QR codes for easy access to personalized career paths
- **Database Integration**: Persistent data storage with PostgreSQL and Prisma ORM
- **Real-time Progress Tracking**: Visual progress indicator showing student journey completion
- **Responsive Design**: Optimized for desktop and tablet use during recruitment events
- **Feedback System**: User feedback collection for continuous improvement
- **Terms & Conditions Integration**: Built-in privacy policy and terms acceptance workflow

## 🚀 Technology Stack

- **Framework**: [Next.js 15](https://nextjs.org/) with App Router and Turbopack
- **Runtime**: [React 19](https://react.dev/) with latest features
- **Language**: [TypeScript](https://www.typescriptlang.org/) with strict mode
- **Styling**: [Tailwind CSS v4](https://tailwindcss.com/) with custom configuration
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/) with Radix UI primitives
- **Database**: [PostgreSQL](https://www.postgresql.org/) with [NeonDB](https://neon.tech/)
- **ORM**: [Prisma](https://www.prisma.io/) for type-safe database operations
- **AI Integration**: [Google Gemini](https://ai.google.dev/) via AI SDK
- **Email Service**: [Resend](https://resend.com/) for transactional emails
- **State Management**: [Zustand](https://zustand-demo.pmnd.rs/) for client state
- **Form Handling**: [React Hook Form](https://react-hook-form.com/) with Zod validation
- **Package Manager**: [pnpm](https://pnpm.io/) for efficient dependency management

## 📋 Prerequisites

- Node.js 18.x or later
- pnpm 9.x or later (recommended package manager)
- PostgreSQL database (or NeonDB account)

## 🛠️ Installation

1. Clone the repository:

```bash
git clone https://github.com/zeyu-chen/acu-ai-coach.git
cd acu-ai-coach
```

2. Install dependencies using pnpm:

```bash
pnpm install
```

3. Set up environment variables:

Create a `.env.local` file in the root directory with the following variables:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/acu_ai_coach"

# AI Integration
GOOGLE_GENERATIVE_AI_API_KEY="your_google_gemini_api_key"

# Email Service
RESEND_API_KEY="your_resend_api_key"
RESEND_FROM_EMAIL="<EMAIL>"

# Application
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

4. Set up the database:

```bash
# Generate Prisma client
pnpm prisma generate

# Run database migrations
pnpm prisma migrate dev

# (Optional) Seed the database
pnpm prisma db seed
```

5. Run the development server:

```bash
pnpm dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
├── src/
│   ├── app/                    # Next.js 15 App Router
│   │   ├── api/               # API routes
│   │   │   ├── chat/          # AI chat endpoint
│   │   │   ├── users/         # User management APIs
│   │   │   └── send-welcome-email/ # Email service
│   │   ├── career/            # Career path pages
│   │   │   └── [user_id]/     # Dynamic user career pages
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/
│   │   ├── steps/             # Multi-step form components
│   │   │   ├── Step0.tsx      # Landing page
│   │   │   ├── Step1.tsx      # Personal information
│   │   │   ├── Step2.tsx      # Dream job selection
│   │   │   ├── Step3.tsx      # Interests & preferences
│   │   │   ├── Step4.tsx      # AI coach introduction
│   │   │   ├── Step5.tsx      # AI chat interface
│   │   │   └── Step6.tsx      # Completion & QR code
│   │   ├── ui/                # shadcn/ui components
│   │   ├── FeedbackModal.tsx  # User feedback modal
│   │   ├── Header.tsx         # Application header
│   │   ├── Footer.tsx         # Application footer
│   │   └── ProgressIndicator.tsx # Progress tracking
│   └── lib/
│       ├── hooks/             # Custom React hooks
│       ├── prisma.ts          # Prisma client configuration
│       ├── store.ts           # Zustand state management
│       ├── types.ts           # TypeScript type definitions
│       ├── utils.ts           # Utility functions
│       └── validation.ts      # Zod schemas
├── prisma/
│   ├── migrations/            # Database migrations
│   └── schema.prisma          # Database schema
├── public/                    # Static assets
│   ├── progress-indicator/    # Step indicator icons
│   ├── acu-logo.png          # University branding
│   └── ...                   # Other static files
├── docs/                      # Documentation
│   ├── acu.txt               # ACU program information
│   └── acu-brochure.pdf      # Marketing materials
├── package.json              # Dependencies and scripts
├── tailwind.config.ts        # Tailwind CSS configuration
├── tsconfig.json             # TypeScript configuration
└── next.config.ts            # Next.js configuration
```

## 🔧 Environment Variables

The application requires the following environment variables:

### Required Variables

| Variable                       | Description                           | Example                                    |
| ------------------------------ | ------------------------------------- | ------------------------------------------ |
| `DATABASE_URL`                 | PostgreSQL connection string          | `postgresql://user:pass@localhost:5432/db` |
| `GOOGLE_GENERATIVE_AI_API_KEY` | Google Gemini API key for AI features | `AIza...`                                  |
| `RESEND_API_KEY`               | Resend API key for email service      | `re_...`                                   |
| `RESEND_FROM_EMAIL`            | Sender email address                  | `<EMAIL>`                   |

### Optional Variables

| Variable              | Description          | Default                 |
| --------------------- | -------------------- | ----------------------- |
| `NEXT_PUBLIC_APP_URL` | Application base URL | `http://localhost:3000` |
| `NODE_ENV`            | Environment mode     | `development`           |

### Setting Up Services

1. **Database (NeonDB)**:

   - Create account at [neon.tech](https://neon.tech)
   - Create new project and copy connection string
   - Set as `DATABASE_URL`

2. **AI Service (Google Gemini)**:

   - Get API key from [Google AI Studio](https://aistudio.google.com)
   - Set as `GOOGLE_GENERATIVE_AI_API_KEY`

3. **Email Service (Resend)**:
   - Create account at [resend.com](https://resend.com)
   - Generate API key and set as `RESEND_API_KEY`
   - Configure sender domain and set `RESEND_FROM_EMAIL`

## 🎯 User Journey

1. **Landing Page** - Welcome screen with AI career exploration introduction
2. **Personal Details** - Student information collection (name, email, school)
3. **Dream Job** - Career aspirations and target companies
4. **Interests** - Hobby and interest selection with terms acceptance
5. **AI Coach Intro** - Introduction to AI coaching capabilities
6. **AI Chat** - Interactive conversation with AI career coach
7. **Completion** - QR code for additional resources and email confirmation

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Configure environment variables in Vercel dashboard
4. Deploy automatically on push to main branch

### Manual Deployment

```bash
# Build the application
pnpm build

# Start production server
pnpm start
```

### Database Migration

```bash
# Deploy database changes
pnpm prisma migrate deploy

# Generate Prisma client
pnpm prisma generate
```

## 📜 Available Scripts

| Script              | Description                             |
| ------------------- | --------------------------------------- |
| `pnpm dev`          | Start development server with Turbopack |
| `pnpm build`        | Build production application            |
| `pnpm start`        | Start production server                 |
| `pnpm lint`         | Run ESLint code analysis                |
| `pnpm format`       | Format code with Prettier               |
| `pnpm format:check` | Check code formatting                   |

## 🔌 API Endpoints

### User Management

- `POST /api/users/create` - Create new user
- `GET /api/users/[user_id]` - Get user by ID
- `POST /api/users/generate-career-path` - Generate AI career roadmap

### AI Integration

- `POST /api/chat` - AI chat interface with Google Gemini

### Email Service

- `POST /api/send-welcome-email` - Send welcome email with career path

## 🎨 Design Features

- **Consistent Purple Theme** (#391460) - ACU brand alignment
- **Glassmorphism Effects** - Modern blur backgrounds for cards
- **Responsive Layout** - 1100px max-width cards with proper spacing
- **Accessible UI** - Proper contrast ratios and keyboard navigation
- **Performance Optimized** - Image sizing and lazy loading

## 🔧 Development

### Code Quality

- **ESLint**: Configured with Next.js recommended rules
- **Prettier**: Automatic code formatting with Tailwind CSS plugin
- **TypeScript**: Strict mode enabled for type safety
- **Husky**: Git hooks for pre-commit quality checks (if configured)

### Database Management

```bash
# View database in Prisma Studio
pnpm prisma studio

# Reset database (development only)
pnpm prisma migrate reset

# Deploy schema changes
pnpm prisma db push
```

### Monitoring & Analytics

- **Vercel Analytics**: Built-in performance monitoring
- **Error Tracking**: Console logging for debugging
- **User Feedback**: Integrated feedback collection system

## 🤝 Partnership

Developed in collaboration with **What's On!** for Australian Catholic University recruitment events, helping bridge the gap between high school students and university career opportunities through innovative AI-powered career guidance.

## 📝 License

This project is proprietary software developed for Australian Catholic University. All rights reserved.

## 👥 Contributing

This is a private project developed for ACU. For inquiries or collaboration opportunities, please contact the development team.

## 📞 Support

For technical support or questions about the platform:

- **What's On!**: <EMAIL>
- **Phone**: +61 ***********
- **Website**: [knowwhatson.com](https://knowwhatson.com)

## 🔗 Related Links

- [ACU Computer Science Program](https://www.acu.edu.au)
- [Next.js Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Google Gemini AI](https://ai.google.dev)

---

**Built with ❤️ for ACU students exploring their future careers**
