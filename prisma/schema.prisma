// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model - stores all user information and career paths
model User {
  id                   String    @id @default(cuid())
  user_id              String    @unique // 8-character nanoid
  first_name           String
  last_name            String
  email                String
  high_school          String
  dream_role           String
  dream_company        String
  selected_interests   String[]  // Array of interests
  career_path          Json?     // JSON object containing the generated career path
  qr_code              String?   // SVG string of QR code
  feedback_helpful     Boolean?  // User feedback on career path helpfulness
  created_at           DateTime  @default(now())
  updated_at           DateTime  @updatedAt

  // Relations
  questions            UserQuestion[]

  @@map("users")
}

// UserQuestion model - stores questions asked by users
model UserQuestion {
  id         Int      @id @default(autoincrement())
  user_id    String
  first_name String
  last_name  String
  question   String
  created_at DateTime @default(now())

  // Relations
  user       User     @relation(fields: [user_id], references: [user_id], onDelete: Cascade)

  @@map("user_questions")
}
